from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

class DepositSlip(models.Model):
    _name = 'caisse.deposit.slip'
    _description = 'Bordereau de Versement'
    _order = 'date desc'

    name = fields.Char('Réf<PERSON>rence', required=True, default='Nouveau')
    bank_account_id = fields.Many2one('account.account', 'Compte bancaire', required=True)
    
    company_id = fields.Many2one('res.company', 'Société', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', 'Devise', related='company_id.currency_id')
    
    total_amount = fields.Monetary('Montant total', required=True, currency_field='currency_id')
    date = fields.Date('Date', required=True, default=fields.Date.today)
    description = fields.Text('Description')
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('deposited', 'Déposé')
    ], default='draft')
    
    caisse_id = fields.Many2one('caisse.caisse', 'Caisse')

    @api.model
    def create(self, vals):
        if vals.get('name', 'Nouveau') == 'Nouveau':
            sequence = self.env['ir.sequence'].next_by_code('caisse.deposit.slip')
            vals['name'] = sequence or 'BDV'
        return super().create(vals)
    
    @api.depends('line_ids.amount')
    def _compute_total(self):
        for slip in self:
            slip.total_amount = sum(slip.line_ids.mapped('amount'))
    
    def confirm_slip(self):
        self.state = 'confirmed'
    
    def register_deposit(self):
        # Create bank operation
        bank_op = self.env['caisse.bank.operation'].create({
            'name': self.name,
            'operation_type': 'deposit',
            'bank_account_id': self.bank_account_id.id,
            'caisse_id': self.caisse_id.id,
            'amount': self.total_amount,
            'date': self.date,
            'reference': self.name,
        })
        bank_op.process_operation()
        
        self.state = 'deposited'

class DepositSlipLine(models.Model):
    _name = 'caisse.deposit.slip.line'
    _description = 'Ligne de Bordereau'

    slip_id = fields.Many2one('caisse.deposit.slip', 'Bordereau', required=True)
    company_id = fields.Many2one('res.company', related='slip_id.company_id')
    currency_id = fields.Many2one('res.currency', related='slip_id.currency_id')
    
    description = fields.Char('Description', required=True)
    amount = fields.Monetary('Montant', required=True, currency_field='currency_id')
    movement_id = fields.Many2one('caisse.movement', 'Mouvement lié')

