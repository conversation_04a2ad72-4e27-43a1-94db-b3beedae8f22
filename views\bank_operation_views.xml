<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Bank Operation Tree View -->
    <record id="view_bank_operation_tree" model="ir.ui.view">
        <field name="name">caisse.bank.operation.tree</field>
        <field name="model">caisse.bank.operation</field>
        <field name="arch" type="xml">
            <tree string="Opérations Bancaires">
                <field name="name"/>
                <field name="operation_type"/>
                <field name="bank_account_id"/>
                <field name="caisse_id"/>
                <field name="amount"/>
                <field name="date"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Bank Operation Form View -->
    <record id="view_bank_operation_form" model="ir.ui.view">
        <field name="name">caisse.bank.operation.form</field>
        <field name="model">caisse.bank.operation</field>
        <field name="arch" type="xml">
            <form string="Opération Bancaire">
                <header>
                    <button name="process_operation" type="object" string="Traiter" 
                            class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,processed,reconciled"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="operation_type"/>
                            <field name="bank_account_id"/>
                            <field name="caisse_id"/>
                        </group>
                        <group>
                            <field name="amount"/>
                            <field name="date"/>
                            <field name="check_number" attrs="{'invisible': [('operation_type', '!=', 'check')]}"/>
                            <field name="reference"/>
                        </group>
                    </group>
                    <group attrs="{'invisible': [('state', '=', 'draft')]}">
                        <field name="movement_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Bank Operation Action -->
    <record id="action_bank_operation" model="ir.actions.act_window">
        <field name="name">Opérations Bancaires</field>
        <field name="res_model">caisse.bank.operation</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>