<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Lettrage Tree View -->
    <record id="view_tresorerie_lettrage_tree" model="ir.ui.view">
        <field name="name">tresorerie.lettrage.tree</field>
        <field name="model">tresorerie.lettrage</field>
        <field name="arch" type="xml">
            <tree string="Lettrage des Paiements" decoration-info="state=='draft'" decoration-success="state=='reconciled'" decoration-warning="state=='active'" decoration-danger="state=='cancelled'">
                <field name="name"/>
                <field name="date"/>
                <field name="partner_name"/>
                <field name="amount" widget="monetary"/>
                <field name="remaining_amount" widget="monetary"/>
                <field name="is_partial" widget="boolean_toggle"/>
                <field name="is_complete" widget="boolean_toggle"/>
                <field name="state" widget="badge"/>
                <field name="mainlevee_linked" widget="boolean_toggle"/>
                <field name="vessel_name"/>
            </tree>
        </field>
    </record>

    <!-- Lettrage Form View -->
    <record id="view_tresorerie_lettrage_form" model="ir.ui.view">
        <field name="name">tresorerie.lettrage.form</field>
        <field name="model">tresorerie.lettrage</field>
        <field name="arch" type="xml">
            <form string="Lettrage des Paiements">
                <header>
                    <button name="action_validate_lettrage" type="object" string="Valider" class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="action_cancel_lettrage" type="object" string="Annuler" class="btn-danger" attrs="{'invisible': [('state', 'in', ['cancelled', 'draft'])]}" groups="tresorie.group_treasury_chief"/>
                    <button name="action_create_partial_lettrage" type="object" string="Paiement Partiel" class="btn-info" attrs="{'invisible': ['|', ('is_complete', '=', True), ('state', '!=', 'active')]}"/>
                    <button name="action_view_related_documents" type="object" string="Documents Liés" class="btn-secondary"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,active,reconciled"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_payment" type="object" class="oe_stat_button" icon="fa-money">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Paiement</span>
                            </div>
                        </button>
                        <button name="action_view_invoice" type="object" class="oe_stat_button" icon="fa-file-text-o">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Facture</span>
                            </div>
                        </button>
                        <button name="action_view_bordereau" type="object" class="oe_stat_button" icon="fa-file-o">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Bordereau</span>
                            </div>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <!-- Status indicators -->
                    <div class="alert alert-success" role="alert" attrs="{'invisible': [('is_complete', '=', False)]}">
                        <strong>Lettrage Complet!</strong> La facture est entièrement lettrée.
                    </div>
                    
                    <div class="alert alert-warning" role="alert" attrs="{'invisible': [('is_partial', '=', False)]}">
                        <strong>Lettrage Partiel</strong> Il reste un montant à lettrer.
                    </div>
                    
                    <group>
                        <group>
                            <field name="date"/>
                            <field name="payment_id" readonly="1"/>
                            <field name="invoice_id" readonly="1"/>
                            <field name="bordereau_id" readonly="1"/>
                        </group>
                        <group>
                            <field name="partner_id" readonly="1"/>
                            <field name="partner_name" readonly="1"/>
                            <field name="vessel_name" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Montants">
                            <group>
                                <group>
                                    <field name="invoice_amount" widget="monetary" readonly="1"/>
                                    <field name="payment_amount" widget="monetary" readonly="1"/>
                                    <field name="amount" widget="monetary"/>
                                    <field name="currency_id" invisible="1"/>
                                    <field name="company_id" invisible="1"/>
                                </group>
                                <group>
                                    <field name="remaining_amount" widget="monetary" readonly="1"/>
                                    <field name="is_partial" readonly="1"/>
                                    <field name="is_complete" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Mainlevée">
                            <group>
                                <group>
                                    <field name="mainlevee_reference" readonly="1"/>
                                    <field name="mainlevee_linked" readonly="1"/>
                                </group>
                                <group>
                                    <field name="port_fee_id" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Validation">
                            <group>
                                <group>
                                    <field name="created_by" readonly="1"/>
                                    <field name="validated_by" readonly="1"/>
                                    <field name="validation_date" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Lettrage Multiple" attrs="{'invisible': [('is_parent', '=', False)]}">
                            <field name="child_lettrage_ids" readonly="1">
                                <tree>
                                    <field name="name"/>
                                    <field name="date"/>
                                    <field name="amount" widget="monetary"/>
                                    <field name="state"/>
                                </tree>
                            </field>
                        </page>
                        
                        <page string="Lettrage Parent" attrs="{'invisible': [('parent_lettrage_id', '=', False)]}">
                            <group>
                                <field name="parent_lettrage_id" readonly="1"/>
                                <field name="is_parent" readonly="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Lettrage Search View -->
    <record id="view_tresorerie_lettrage_search" model="ir.ui.view">
        <field name="name">tresorerie.lettrage.search</field>
        <field name="model">tresorerie.lettrage</field>
        <field name="arch" type="xml">
            <search string="Rechercher Lettrages">
                <field name="name" string="Référence"/>
                <field name="partner_name" string="Redevable"/>
                <field name="vessel_name" string="Navire"/>
                <field name="mainlevee_reference" string="Mainlevée"/>
                <field name="payment_id" string="Paiement"/>
                <field name="invoice_id" string="Facture"/>
                
                <filter name="draft" string="Brouillon" domain="[('state', '=', 'draft')]"/>
                <filter name="active" string="Actif" domain="[('state', '=', 'active')]"/>
                <filter name="reconciled" string="Rapproché" domain="[('state', '=', 'reconciled')]"/>
                <filter name="cancelled" string="Annulé" domain="[('state', '=', 'cancelled')]"/>
                
                <separator/>
                <filter name="partial" string="Lettrage Partiel" domain="[('is_partial', '=', True)]"/>
                <filter name="complete" string="Lettrage Complet" domain="[('is_complete', '=', True)]"/>
                <filter name="with_mainlevee" string="Avec Mainlevée" domain="[('mainlevee_linked', '=', True)]"/>
                
                <separator/>
                <filter name="today" string="Aujourd'hui" domain="[('date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('date', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <filter name="this_week" string="Cette semaine" domain="[('date', '&gt;=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                <filter name="this_month" string="Ce mois" domain="[('date', '&gt;=', context_today().strftime('%Y-%m-01'))]"/>
                
                <group expand="0" string="Grouper par">
                    <filter name="group_state" string="Statut" context="{'group_by': 'state'}"/>
                    <filter name="group_partner" string="Redevable" context="{'group_by': 'partner_id'}"/>
                    <filter name="group_is_partial" string="Type de lettrage" context="{'group_by': 'is_partial'}"/>
                    <filter name="group_mainlevee" string="Mainlevée" context="{'group_by': 'mainlevee_linked'}"/>
                    <filter name="group_date" string="Date" context="{'group_by': 'date'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Lettrage Kanban View -->
    <record id="view_tresorerie_lettrage_kanban" model="ir.ui.view">
        <field name="name">tresorerie.lettrage.kanban</field>
        <field name="model">tresorerie.lettrage</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" class="o_kanban_small_column">
                <field name="name"/>
                <field name="partner_name"/>
                <field name="amount"/>
                <field name="remaining_amount"/>
                <field name="is_partial"/>
                <field name="is_complete"/>
                <field name="state"/>
                <field name="currency_id"/>
                <field name="vessel_name"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div><strong><field name="partner_name"/></strong></div>
                                    <div><field name="vessel_name"/></div>
                                    <div t-if="record.is_partial.raw_value" class="text-warning">
                                        <i class="fa fa-exclamation-triangle"/> Partiel
                                    </div>
                                    <div t-if="record.is_complete.raw_value" class="text-success">
                                        <i class="fa fa-check"/> Complet
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_record_bottom">
                                <div class="oe_kanban_bottom_left">
                                    <field name="amount" widget="monetary"/>
                                </div>
                                <div class="oe_kanban_bottom_right" t-if="record.remaining_amount.raw_value > 0">
                                    <span class="text-muted">Reste: </span>
                                    <field name="remaining_amount" widget="monetary"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_tresorerie_lettrage" model="ir.actions.act_window">
        <field name="name">Lettrage des Paiements</field>
        <field name="res_model">tresorerie.lettrage</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="context">{'search_default_this_month': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Aucun lettrage trouvé
            </p>
            <p>
                Les lettrages sont créés automatiquement lors de la validation des paiements
                pour associer les paiements aux factures et mainlevées.
            </p>
        </field>
    </record>

    <!-- Action for partial lettrages -->
    <record id="action_lettrage_partial" model="ir.actions.act_window">
        <field name="name">Lettrages Partiels</field>
        <field name="res_model">tresorerie.lettrage</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('is_partial', '=', True), ('state', '!=', 'cancelled')]</field>
    </record>

    <!-- Action for complete lettrages -->
    <record id="action_lettrage_complete" model="ir.actions.act_window">
        <field name="name">Lettrages Complets</field>
        <field name="res_model">tresorerie.lettrage</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('is_complete', '=', True), ('state', '!=', 'cancelled')]</field>
    </record>
</odoo>
