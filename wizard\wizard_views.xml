<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Payment Reject Wizard View -->
    <record id="view_payment_reject_wizard_form" model="ir.ui.view">
        <field name="name">tresorerie.payment.reject.wizard.form</field>
        <field name="model">tresorerie.payment.reject.wizard</field>
        <field name="arch" type="xml">
            <form string="Rejeter le Paiement">
                <group>
                    <field name="payment_id" readonly="1"/>
                    <field name="rejection_reason" placeholder="Expliquez le motif du rejet..."/>
                    <field name="notify_cashier"/>
                </group>
                <footer>
                    <button name="action_reject_payment" type="object" string="Rejeter" class="btn-danger"/>
                    <button string="Annuler" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Partial Payment Wizard View -->
    <record id="view_partial_payment_wizard_form" model="ir.ui.view">
        <field name="name">tresorerie.partial.payment.wizard.form</field>
        <field name="model">tresorerie.partial.payment.wizard</field>
        <field name="arch" type="xml">
            <form string="Créer un Paiement Partiel">
                <group>
                    <group>
                        <field name="lettrage_id" readonly="1"/>
                        <field name="remaining_amount" widget="monetary"/>
                        <field name="partial_amount" widget="monetary"/>
                        <field name="currency_id" invisible="1"/>
                    </group>
                    <group>
                        <field name="payment_method" widget="radio"/>
                    </group>
                </group>
                
                <group string="Détails Chèque" attrs="{'invisible': [('payment_method', '!=', 'check')]}">
                    <group>
                        <field name="check_number" attrs="{'required': [('payment_method', '=', 'check')]}"/>
                        <field name="check_date" attrs="{'required': [('payment_method', '=', 'check')]}"/>
                    </group>
                    <group>
                        <field name="bank_id" attrs="{'required': [('payment_method', '=', 'check')]}" options="{'no_create': True}"/>
                    </group>
                </group>
                
                <group string="Détails Virement" attrs="{'invisible': [('payment_method', '!=', 'transfer')]}">
                    <field name="transfer_reference" attrs="{'required': [('payment_method', '=', 'transfer')]}"/>
                </group>
                
                <footer>
                    <button name="action_create_partial_payment" type="object" string="Créer le Paiement" class="btn-primary"/>
                    <button string="Annuler" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Actions for Wizards -->
    <record id="action_payment_reject_wizard" model="ir.actions.act_window">
        <field name="name">Rejeter le Paiement</field>
        <field name="res_model">tresorerie.payment.reject.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <record id="action_partial_payment_wizard" model="ir.actions.act_window">
        <field name="name">Paiement Partiel</field>
        <field name="res_model">tresorerie.partial.payment.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
