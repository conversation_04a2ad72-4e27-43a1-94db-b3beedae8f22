from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

class TresorerieRefund(models.Model):
    _name = 'tresorerie.refund'
    _description = 'Remboursement Trésorerie'
    _order = 'date desc, name desc'
    _rec_name = 'name'

    name = fields.Char('Numéro de Remboursement', required=True, default='Nouveau', readonly=True)
    date = fields.Datetime('Date de Demande', required=True, default=fields.Datetime.now)
    
    # Références du paiement original
    payment_id = fields.Many2one('tresorerie.payment', 'Paiement Original', required=True)
    bordereau_id = fields.Many2one('tresorerie.bordereau', 'Bordereau', related='payment_id.bordereau_id', store=True)
    invoice_id = fields.Many2one('account.move', 'Facture', related='payment_id.invoice_id', store=True)
    receipt_id = fields.Many2one('tresorerie.receipt', 'Reçu Original', related='payment_id.receipt_id', store=True)
    
    # Informations du redevable
    partner_id = fields.Many2one('res.partner', 'Redevable', related='payment_id.partner_id', store=True)
    partner_name = fields.Char('Nom du redevable', related='partner_id.name', store=True)
    
    # Montants
    company_id = fields.Many2one('res.company', 'Société', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', 'Devise', related='company_id.currency_id')
    
    original_amount = fields.Monetary('Montant Original', related='payment_id.amount_paid', store=True, currency_field='currency_id')
    refund_amount = fields.Monetary('Montant à Rembourser', required=True, currency_field='currency_id')
    remaining_amount = fields.Monetary('Montant Restant', compute='_compute_remaining_amount', store=True, currency_field='currency_id')
    
    # Type de remboursement
    refund_type = fields.Selection([
        ('partial', 'Remboursement Partiel'),
        ('total', 'Remboursement Total')
    ], string='Type de Remboursement', required=True, compute='_compute_refund_type', store=True)
    
    # Motif du remboursement
    reason = fields.Selection([
        ('overpayment', 'Trop-perçu'),
        ('cancellation', 'Annulation de service'),
        ('error', 'Erreur de facturation'),
        ('duplicate', 'Paiement en double'),
        ('other', 'Autre')
    ], string='Motif', required=True)
    
    reason_description = fields.Text('Description du Motif', required=True)
    
    # Mode de remboursement
    refund_method = fields.Selection([
        ('cash', 'Espèces'),
        ('check', 'Chèque'),
        ('transfer', 'Virement bancaire')
    ], string='Mode de Remboursement', required=True)
    
    # Détails du remboursement
    check_number = fields.Char('Numéro de chèque')
    check_date = fields.Date('Date du chèque')
    bank_account = fields.Char('Compte bancaire bénéficiaire')
    transfer_reference = fields.Char('Référence de virement')
    
    # Caisse
    caisse_id = fields.Many2one('caisse.caisse', 'Caisse', required=True)
    
    # Workflow et validation
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('submitted', 'Soumis'),
        ('approved', 'Approuvé'),
        ('processed', 'Traité'),
        ('rejected', 'Rejeté'),
        ('cancelled', 'Annulé')
    ], default='draft', string='Statut', tracking=True)
    
    # Validation hiérarchique
    requested_by = fields.Many2one('res.users', 'Demandé par', default=lambda self: self.env.user, readonly=True)
    approved_by = fields.Many2one('res.users', 'Approuvé par')
    approval_date = fields.Datetime('Date d\'approbation')
    processed_by = fields.Many2one('res.users', 'Traité par')
    processing_date = fields.Datetime('Date de traitement')
    
    # Rejet
    rejection_reason = fields.Text('Motif de rejet')
    rejected_by = fields.Many2one('res.users', 'Rejeté par')
    rejection_date = fields.Datetime('Date de rejet')
    
    # Documents justificatifs
    attachment_ids = fields.Many2many('ir.attachment', string='Pièces Justificatives')
    
    # Mouvement de caisse associé
    movement_id = fields.Many2one('caisse.movement', 'Mouvement de Caisse')
    
    # Audit trail
    created_date = fields.Datetime('Date de création', default=fields.Datetime.now, readonly=True)
    
    @api.depends('original_amount', 'refund_amount')
    def _compute_remaining_amount(self):
        for refund in self:
            refund.remaining_amount = refund.original_amount - refund.refund_amount
    
    @api.depends('refund_amount', 'original_amount')
    def _compute_refund_type(self):
        for refund in self:
            if refund.refund_amount == refund.original_amount:
                refund.refund_type = 'total'
            else:
                refund.refund_type = 'partial'
    
    @api.model
    def create(self, vals):
        if vals.get('name', 'Nouveau') == 'Nouveau':
            sequence = self.env['ir.sequence'].next_by_code('tresorerie.refund')
            vals['name'] = sequence or 'RMB'
        return super().create(vals)
    
    @api.onchange('payment_id')
    def _onchange_payment_id(self):
        if self.payment_id:
            self.refund_amount = self.payment_id.amount_paid
            self.caisse_id = self.payment_id.caisse_id
    
    @api.onchange('refund_method')
    def _onchange_refund_method(self):
        if self.refund_method != 'check':
            self.check_number = False
            self.check_date = False
        if self.refund_method != 'transfer':
            self.bank_account = False
            self.transfer_reference = False
    
    def action_submit(self):
        """Soumettre la demande de remboursement"""
        self.ensure_one()
        
        if self.state != 'draft':
            raise UserError(_('Seules les demandes en brouillon peuvent être soumises.'))
        
        # Vérifications
        if self.refund_amount <= 0:
            raise UserError(_('Le montant à rembourser doit être positif.'))
        
        if self.refund_amount > self.original_amount:
            raise UserError(_('Le montant à rembourser ne peut pas dépasser le montant original.'))
        
        if not self.reason_description:
            raise UserError(_('La description du motif est obligatoire.'))
        
        self.state = 'submitted'
        
        # Notification aux superviseurs
        supervisors = self.env['res.users'].search([
            ('groups_id', 'in', self.env.ref('tresorie.group_treasury_chief').id)
        ])
        
        for supervisor in supervisors:
            self.activity_schedule(
                'mail.mail_activity_data_todo',
                user_id=supervisor.id,
                summary=_('Demande de remboursement à approuver'),
                note=_('Demande de remboursement %s - Montant: %s') % (self.name, self.refund_amount)
            )
        
        self.message_post(
            body=_("Demande soumise par %s") % self.env.user.name,
            message_type='notification'
        )
    
    def action_approve(self):
        """Approuver la demande de remboursement"""
        self.ensure_one()
        
        # Vérifier les permissions
        if not self.env.user.has_group('tresorie.group_treasury_chief'):
            raise UserError(_('Seul le chef de trésorerie peut approuver les remboursements.'))
        
        if self.state != 'submitted':
            raise UserError(_('Seules les demandes soumises peuvent être approuvées.'))
        
        self.state = 'approved'
        self.approved_by = self.env.user
        self.approval_date = fields.Datetime.now()
        
        self.message_post(
            body=_("Demande approuvée par %s") % self.env.user.name,
            message_type='notification'
        )
    
    def action_process(self):
        """Traiter le remboursement"""
        self.ensure_one()
        
        if self.state != 'approved':
            raise UserError(_('Seules les demandes approuvées peuvent être traitées.'))
        
        # Vérifier le solde de la caisse
        if self.caisse_id.balance < self.refund_amount:
            raise UserError(_('Solde insuffisant dans la caisse pour effectuer le remboursement.'))
        
        # Créer le mouvement de caisse
        movement = self.env['caisse.movement'].create({
            'name': f'Remboursement - {self.name}',
            'caisse_id': self.caisse_id.id,
            'type': 'out',
            'category': 'refund',
            'amount': self.refund_amount,
            'description': f'Remboursement {self.refund_type} - {self.partner_name} - Motif: {self.reason_description}',
            'date': fields.Datetime.now(),
            'refund_id': self.id,
        })
        
        self.movement_id = movement.id
        self.state = 'processed'
        self.processed_by = self.env.user
        self.processing_date = fields.Datetime.now()
        
        # Valider le mouvement
        movement.validate_movement()
        
        self.message_post(
            body=_("Remboursement traité par %s - Montant: %s") % (self.env.user.name, self.refund_amount),
            message_type='notification'
        )
    
    def action_reject(self):
        """Rejeter la demande de remboursement"""
        self.ensure_one()
        
        if not self.env.user.has_group('tresorie.group_treasury_chief'):
            raise UserError(_('Seul le chef de trésorerie peut rejeter les remboursements.'))
        
        if self.state not in ['submitted', 'approved']:
            raise UserError(_('Seules les demandes soumises ou approuvées peuvent être rejetées.'))
        
        # Ouvrir un wizard pour saisir le motif de rejet
        return {
            'type': 'ir.actions.act_window',
            'name': _('Motif de rejet'),
            'res_model': 'tresorerie.refund.reject.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_refund_id': self.id}
        }
    
    def action_cancel(self):
        """Annuler la demande de remboursement"""
        self.ensure_one()
        
        if self.state == 'processed':
            raise UserError(_('Impossible d\'annuler un remboursement déjà traité.'))
        
        self.state = 'cancelled'
        
        self.message_post(
            body=_("Demande annulée par %s") % self.env.user.name,
            message_type='notification'
        )
    
    def action_reset_to_draft(self):
        """Remettre en brouillon"""
        self.ensure_one()
        
        if self.state == 'processed':
            raise UserError(_('Impossible de remettre en brouillon un remboursement traité.'))
        
        self.state = 'draft'
        self.approved_by = False
        self.approval_date = False
        self.rejected_by = False
        self.rejection_date = False
        self.rejection_reason = False
    
    @api.constrains('refund_amount', 'original_amount')
    def _check_refund_amount(self):
        for refund in self:
            if refund.refund_amount <= 0:
                raise ValidationError(_('Le montant à rembourser doit être positif.'))
            
            if refund.refund_amount > refund.original_amount:
                raise ValidationError(_('Le montant à rembourser ne peut pas dépasser le montant original.'))
    
    def name_get(self):
        result = []
        for record in self:
            name = f"{record.name} - {record.partner_name or 'Sans redevable'}"
            if record.refund_type == 'partial':
                name += " (Partiel)"
            elif record.refund_type == 'total':
                name += " (Total)"
            result.append((record.id, name))
        return result
