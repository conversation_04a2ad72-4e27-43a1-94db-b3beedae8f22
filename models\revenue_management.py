from odoo import models, fields, api, _

class RevenueCategory(models.Model):
    _name = 'caisse.revenue.category'
    _description = 'Catégorie de Recettes'

    name = fields.Char('Nom', required=True)
    code = fields.Char('Code', required=True)
    account_id = fields.Many2one('account.account', 'Compte comptable')
    active = fields.Boolean('Actif', default=True)

class RevenueCollection(models.Model):
    _name = 'caisse.revenue.collection'
    _description = 'Régie de Recettes'
    _order = 'date desc'

    name = fields.Char('Référence', required=True, default='Nouveau')
    date = fields.Date('Date', required=True, default=fields.Date.today)
    
    company_id = fields.Many2one('res.company', 'Société', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', 'Devise', related='company_id.currency_id')
    
    collector_id = fields.Many2one('res.users', 'Régisseur', required=True)
    caisse_id = fields.Many2one('caisse.caisse', 'Caisse', required=True)
    
    line_ids = fields.One2many('caisse.revenue.line', 'collection_id', 'Lignes de recettes')
    total_amount = fields.Monetary('Total', compute='_compute_total', currency_field='currency_id')
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('validated', 'Validé'),
        ('deposited', 'Versé')
    ], default='draft')
    
    @api.model
    def create(self, vals):
        if vals.get('name', 'Nouveau') == 'Nouveau':
            sequence = self.env['ir.sequence'].next_by_code('caisse.revenue.collection')
            vals['name'] = sequence or 'REC'
        return super().create(vals)
    
    @api.depends('line_ids.amount')
    def _compute_total(self):
        for collection in self:
            collection.total_amount = sum(collection.line_ids.mapped('amount'))
    
    def validate_collection(self):
        self.state = 'validated'
    
    def deposit_collection(self):
        # Create movements for each line
        for line in self.line_ids:
            movement = self.env['caisse.movement'].create({
                'name': f'Recette - {line.description}',
                'caisse_id': self.caisse_id.id,
                'type': 'in',
                'category': 'other',
                'amount': line.amount,
                'description': f'Régie de recettes - {line.description}',
            })
            movement.validate_movement()
        
        self.state = 'deposited'

class RevenueLine(models.Model):
    _name = 'caisse.revenue.line'
    _description = 'Ligne de Recette'

    collection_id = fields.Many2one('caisse.revenue.collection', 'Régie', required=True)
    company_id = fields.Many2one('res.company', related='collection_id.company_id')
    currency_id = fields.Many2one('res.currency', related='collection_id.currency_id')
    
    category_id = fields.Many2one('caisse.revenue.category', 'Catégorie', required=True)
    description = fields.Char('Description', required=True)
    amount = fields.Monetary('Montant', required=True, currency_field='currency_id')
    partner_id = fields.Many2one('res.partner', 'Payeur')
