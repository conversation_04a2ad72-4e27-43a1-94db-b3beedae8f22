from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime

class TresoreriePayment(models.Model):
    _name = 'tresorerie.payment'
    _description = 'Paiement Trésorerie'
    _order = 'date desc, name desc'
    _rec_name = 'name'

    name = fields.Char('Numéro de Paiement', required=True, default='Nouveau', readonly=True)
    date = fields.Datetime('Date de Paiement', required=True, default=fields.Datetime.now)
    
    # Références
    bordereau_id = fields.Many2one('tresorerie.bordereau', 'Bordereau', required=True)
    invoice_id = fields.Many2one('account.move', 'Facture', required=True, domain=[('move_type', '=', 'out_invoice')])
    port_fee_id = fields.Many2one('caisse.port.fee', 'Redevance Portuaire', related='bordereau_id.port_fee_id', store=True)
    
    # Informations du redevable
    partner_id = fields.Many2one('res.partner', 'Redevable', related='bordereau_id.partner_id', store=True)
    partner_name = fields.Char('Nom du redevable', related='partner_id.name', store=True)
    
    # Montants
    company_id = fields.Many2one('res.company', 'Société', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', 'Devise', related='company_id.currency_id')
    amount_due = fields.Monetary('Montant dû', related='bordereau_id.amount', store=True, currency_field='currency_id')
    amount_paid = fields.Monetary('Montant payé', required=True, currency_field='currency_id')
    amount_remaining = fields.Monetary('Montant restant', compute='_compute_amount_remaining', store=True, currency_field='currency_id')
    
    # Mode de paiement
    payment_method = fields.Selection([
        ('cash', 'Espèces'),
        ('check', 'Chèque'),
        ('transfer', 'Virement bancaire'),
        ('card', 'Carte bancaire')
    ], string='Mode de paiement', required=True)
    
    # Détails selon le mode de paiement
    check_number = fields.Char('Numéro de chèque')
    check_date = fields.Date('Date du chèque')
    bank_id = fields.Many2one('res.bank', 'Banque émettrice')
    transfer_reference = fields.Char('Référence de virement')
    
    # Caisse et session
    caisse_id = fields.Many2one('caisse.caisse', 'Caisse', required=True)
    session_id = fields.Many2one('caisse.session', 'Session de caisse')
    
    # Statut et workflow
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('pending', 'En attente de validation'),
        ('validated', 'Validé'),
        ('rejected', 'Rejeté'),
        ('cancelled', 'Annulé')
    ], default='draft', string='Statut', tracking=True)
    
    # Validation
    validated_by = fields.Many2one('res.users', 'Validé par')
    validation_date = fields.Datetime('Date de validation')
    rejection_reason = fields.Text('Motif de rejet')
    
    # Reçu
    receipt_id = fields.Many2one('tresorerie.receipt', 'Reçu généré')
    receipt_number = fields.Char('Numéro de reçu', related='receipt_id.name', store=True)
    
    # Lettrage
    lettrage_id = fields.Many2one('tresorerie.lettrage', 'Lettrage')
    is_partial_payment = fields.Boolean('Paiement partiel', compute='_compute_is_partial_payment', store=True)
    
    # Audit trail
    cashier_id = fields.Many2one('res.users', 'Caissier', default=lambda self: self.env.user, readonly=True)
    created_date = fields.Datetime('Date de création', default=fields.Datetime.now, readonly=True)
    
    # Contrôles avant paiement
    invoice_verified = fields.Boolean('Facture vérifiée', default=False)
    bordereau_verified = fields.Boolean('Bordereau vérifié', default=False)
    mainlevee_verified = fields.Boolean('Mainlevée vérifiée', default=False)
    all_documents_verified = fields.Boolean('Tous documents vérifiés', compute='_compute_all_documents_verified', store=True)
    
    @api.depends('amount_due', 'amount_paid')
    def _compute_amount_remaining(self):
        for payment in self:
            payment.amount_remaining = payment.amount_due - payment.amount_paid
    
    @api.depends('amount_due', 'amount_paid')
    def _compute_is_partial_payment(self):
        for payment in self:
            payment.is_partial_payment = payment.amount_paid < payment.amount_due
    
    @api.depends('invoice_verified', 'bordereau_verified', 'mainlevee_verified')
    def _compute_all_documents_verified(self):
        for payment in self:
            payment.all_documents_verified = (
                payment.invoice_verified and 
                payment.bordereau_verified and 
                payment.mainlevee_verified
            )
    
    @api.model
    def create(self, vals):
        if vals.get('name', 'Nouveau') == 'Nouveau':
            sequence = self.env['ir.sequence'].next_by_code('tresorerie.payment')
            vals['name'] = sequence or 'PAY'
        return super().create(vals)
    
    @api.onchange('bordereau_id')
    def _onchange_bordereau_id(self):
        if self.bordereau_id:
            self.invoice_id = self.bordereau_id.invoice_id
            self.amount_paid = self.bordereau_id.amount
    
    @api.onchange('payment_method')
    def _onchange_payment_method(self):
        if self.payment_method != 'check':
            self.check_number = False
            self.check_date = False
            self.bank_id = False
        if self.payment_method != 'transfer':
            self.transfer_reference = False
    
    def action_verify_documents(self):
        """Vérifier les documents avant paiement"""
        self.ensure_one()
        
        # Vérifier la facture
        if not self.invoice_id:
            raise UserError(_('Aucune facture associée.'))
        if self.invoice_id.state != 'posted':
            raise UserError(_('La facture doit être validée.'))
        self.invoice_verified = True
        
        # Vérifier le bordereau
        if not self.bordereau_id:
            raise UserError(_('Aucun bordereau associé.'))
        if self.bordereau_id.state != 'signed':
            raise UserError(_('Le bordereau doit être signé.'))
        self.bordereau_verified = True
        
        # Vérifier la mainlevée (si applicable)
        if self.bordereau_id.mainlevee_reference:
            self.mainlevee_verified = True
        else:
            # Si pas de référence de mainlevée, on considère comme vérifié
            self.mainlevee_verified = True
        
        self.message_post(
            body=_("Documents vérifiés par %s") % self.env.user.name,
            message_type='notification'
        )
    
    def action_submit_for_validation(self):
        """Soumettre pour validation"""
        self.ensure_one()
        
        if not self.all_documents_verified:
            raise UserError(_('Tous les documents doivent être vérifiés avant soumission.'))
        
        if self.amount_paid <= 0:
            raise UserError(_('Le montant payé doit être positif.'))
        
        self.state = 'pending'
        
        # Notification au chef de trésorerie
        treasury_chiefs = self.env['res.users'].search([
            ('groups_id', 'in', self.env.ref('tresorie.group_treasury_chief').id)
        ])
        
        for chief in treasury_chiefs:
            self.activity_schedule(
                'mail.mail_activity_data_todo',
                user_id=chief.id,
                summary=_('Validation de paiement requise'),
                note=_('Paiement %s en attente de validation') % self.name
            )
    
    def action_validate(self):
        """Valider le paiement (Chef de trésorerie uniquement)"""
        self.ensure_one()
        
        # Vérifier les permissions
        if not self.env.user.has_group('tresorie.group_treasury_chief'):
            raise UserError(_('Seul le chef de trésorerie peut valider les paiements.'))
        
        if self.state != 'pending':
            raise UserError(_('Seuls les paiements en attente peuvent être validés.'))
        
        self.state = 'validated'
        self.validated_by = self.env.user
        self.validation_date = fields.Datetime.now()
        
        # Générer le reçu automatiquement
        self._generate_receipt()
        
        # Créer le lettrage automatique
        self._create_lettrage()
        
        # Mettre à jour le bordereau
        if self.bordereau_id:
            self.bordereau_id.state = 'paid'
            self.bordereau_id.payment_date = self.validation_date
            self.bordereau_id.payment_id = self.id
        
        # Créer le mouvement de caisse
        self._create_caisse_movement()
        
        self.message_post(
            body=_("Paiement validé par %s") % self.env.user.name,
            message_type='notification'
        )
    
    def action_reject(self):
        """Rejeter le paiement"""
        self.ensure_one()
        
        if not self.env.user.has_group('tresorie.group_treasury_chief'):
            raise UserError(_('Seul le chef de trésorerie peut rejeter les paiements.'))
        
        if self.state != 'pending':
            raise UserError(_('Seuls les paiements en attente peuvent être rejetés.'))
        
        # Ouvrir un wizard pour saisir le motif de rejet
        return {
            'type': 'ir.actions.act_window',
            'name': _('Motif de rejet'),
            'res_model': 'tresorerie.payment.reject.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_payment_id': self.id}
        }
    
    def _generate_receipt(self):
        """Générer le reçu automatiquement"""
        receipt = self.env['tresorerie.receipt'].create({
            'payment_id': self.id,
            'partner_id': self.partner_id.id,
            'amount': self.amount_paid,
            'payment_method': self.payment_method,
            'date': self.validation_date,
        })
        self.receipt_id = receipt.id
    
    def _create_lettrage(self):
        """Créer le lettrage automatique"""
        lettrage = self.env['tresorerie.lettrage'].create({
            'payment_id': self.id,
            'invoice_id': self.invoice_id.id,
            'bordereau_id': self.bordereau_id.id,
            'amount': self.amount_paid,
            'is_partial': self.is_partial_payment,
        })
        self.lettrage_id = lettrage.id
    
    def _create_caisse_movement(self):
        """Créer le mouvement de caisse"""
        self.env['caisse.movement'].create({
            'name': f'Encaissement - {self.name}',
            'caisse_id': self.caisse_id.id,
            'type': 'in',
            'category': 'payment',
            'amount': self.amount_paid,
            'description': f'Paiement {self.payment_method} - {self.partner_name}',
            'date': self.validation_date,
            'payment_id': self.id,
        })
    
    @api.constrains('amount_paid')
    def _check_amount_paid(self):
        for payment in self:
            if payment.amount_paid <= 0:
                raise ValidationError(_('Le montant payé doit être positif.'))
            if payment.amount_paid > payment.amount_due:
                raise ValidationError(_('Le montant payé ne peut pas dépasser le montant dû.'))
