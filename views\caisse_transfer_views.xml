<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Transfer Form View -->
    <record id="view_caisse_transfer_form" model="ir.ui.view">
        <field name="name">caisse.transfer.form</field>
        <field name="model">caisse.transfer</field>
        <field name="arch" type="xml">
            <form string="Virement de Caisse">
                <header>
                    <button name="execute_transfer" type="object" string="Exécuter" 
                            class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="cancel_transfer" type="object" string="Annuler" 
                            attrs="{'invisible': [('state', 'in', ['cancelled'])]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,done,cancelled"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="date"/>
                            <field name="amount"/>
                        </group>
                        <group>
                            <field name="source_caisse_id"/>
                            <field name="destination_caisse_id"/>
                        </group>
                    </group>
                    <group>
                        <field name="description"/>
                    </group>
                    <group attrs="{'invisible': [('state', '=', 'draft')]}">
                        <field name="source_movement_id"/>
                        <field name="destination_movement_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Transfer Tree View -->
    <record id="view_caisse_transfer_tree" model="ir.ui.view">
        <field name="name">caisse.transfer.tree</field>
        <field name="model">caisse.transfer</field>
        <field name="arch" type="xml">
            <tree string="Virements de Caisse">
                <field name="name"/>
                <field name="date"/>
                <field name="source_caisse_id"/>
                <field name="destination_caisse_id"/>
                <field name="amount"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Transfer Action -->
    <record id="action_caisse_transfer" model="ir.actions.act_window">
        <field name="name">Virements de Caisse</field>
        <field name="res_model">caisse.transfer</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>


