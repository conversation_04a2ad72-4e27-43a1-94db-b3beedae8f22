from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

class CaisseTransfer(models.Model):
    _name = 'caisse.transfer'
    _description = 'Virement de Caisse'
    _order = 'date desc'

    name = fields.Char('Réf<PERSON>rence', required=True, default='Nouveau')
    date = fields.Datetime('Date', required=True, default=fields.Datetime.now)
    
    company_id = fields.Many2one('res.company', 'Société', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', 'Devise', related='company_id.currency_id')
    
    source_caisse_id = fields.Many2one('caisse.caisse', 'Caisse source', required=True)
    destination_caisse_id = fields.Many2one('caisse.caisse', 'Caisse destination', required=True)
    
    amount = fields.Monetary('Montant', required=True, currency_field='currency_id')
    description = fields.Text('Description')
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('done', 'Effectué'),
        ('cancelled', 'Annulé')
    ], default='draft')
    
    source_movement_id = fields.Many2one('caisse.movement', 'Mouvement source')
    destination_movement_id = fields.Many2one('caisse.movement', 'Mouvement destination')

    @api.model
    def create(self, vals):
        if vals.get('name', 'Nouveau') == 'Nouveau':
            sequence = self.env['ir.sequence'].next_by_code('caisse.transfer')
            vals['name'] = sequence or 'VIR'
        return super().create(vals)

    @api.constrains('source_caisse_id', 'destination_caisse_id')
    def _check_different_caisses(self):
        for transfer in self:
            if transfer.source_caisse_id == transfer.destination_caisse_id:
                raise ValidationError(_('Les caisses source et destination doivent être différentes.'))

    @api.constrains('amount')
    def _check_amount(self):
        for transfer in self:
            if transfer.amount <= 0:
                raise ValidationError(_('Le montant doit être positif.'))

    def execute_transfer(self):
        self.ensure_one()
        if self.state != 'draft':
            raise UserError(_('Seuls les virements en brouillon peuvent être exécutés.'))
        
        # Create source movement (out)
        source_movement = self.env['caisse.movement'].create({
            'name': f'Virement vers {self.destination_caisse_id.name}',
            'caisse_id': self.source_caisse_id.id,
            'type': 'out',
            'category': 'transfer',
            'amount': self.amount,
            'description': self.description,
            'transfer_id': self.id,
        })
        
        # Create destination movement (in)
        destination_movement = self.env['caisse.movement'].create({
            'name': f'Virement de {self.source_caisse_id.name}',
            'caisse_id': self.destination_caisse_id.id,
            'type': 'in',
            'category': 'transfer',
            'amount': self.amount,
            'description': self.description,
            'transfer_id': self.id,
        })
        
        # Validate movements
        source_movement.validate_movement()
        destination_movement.validate_movement()
        
        self.source_movement_id = source_movement.id
        self.destination_movement_id = destination_movement.id
        self.state = 'done'

    def cancel_transfer(self):
        self.ensure_one()
        if self.state == 'done':
            # Cancel related movements
            if self.source_movement_id:
                self.source_movement_id.state = 'cancelled'
            if self.destination_movement_id:
                self.destination_movement_id.state = 'cancelled'
        self.state = 'cancelled'

    def action_view_movements(self):
        """Voir les mouvements liés"""
        self.ensure_one()
        movement_ids = []
        if self.source_movement_id:
            movement_ids.append(self.source_movement_id.id)
        if self.destination_movement_id:
            movement_ids.append(self.destination_movement_id.id)
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Mouvements liés'),
            'view_mode': 'tree,form',
            'res_model': 'caisse.movement',
            'domain': [('id', 'in', movement_ids)],
            'context': {'default_transfer_id': self.id},
        }




