from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

class TresorerieLettrage(models.Model):
    _name = 'tresorerie.lettrage'
    _description = 'Lettrage des Paiements'
    _order = 'date desc, name desc'
    _rec_name = 'name'

    name = fields.Char('Référence de Lettrage', required=True, default='Nouveau', readonly=True)
    date = fields.Datetime('Date de Lettrage', required=True, default=fields.Datetime.now)
    
    # Références principales
    payment_id = fields.Many2one('tresorerie.payment', 'Paiement', required=True, ondelete='cascade')
    invoice_id = fields.Many2one('account.move', 'Facture', required=True)
    bordereau_id = fields.Many2one('tresorerie.bordereau', 'Bordereau', required=True)
    
    # Informations du redevable
    partner_id = fields.Many2one('res.partner', 'Redevable', related='payment_id.partner_id', store=True)
    partner_name = fields.Char('Nom du redevable', related='partner_id.name', store=True)
    
    # Montants
    company_id = fields.Many2one('res.company', 'Société', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', 'Devise', related='company_id.currency_id')
    
    invoice_amount = fields.Monetary('Montant facture', related='invoice_id.amount_total', store=True, currency_field='currency_id')
    payment_amount = fields.Monetary('Montant payé', related='payment_id.amount_paid', store=True, currency_field='currency_id')
    amount = fields.Monetary('Montant lettré', required=True, currency_field='currency_id')
    remaining_amount = fields.Monetary('Montant restant', compute='_compute_remaining_amount', store=True, currency_field='currency_id')
    
    # Type de lettrage
    is_partial = fields.Boolean('Lettrage partiel', default=False)
    is_complete = fields.Boolean('Lettrage complet', compute='_compute_is_complete', store=True)
    
    # Statut
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('active', 'Actif'),
        ('reconciled', 'Rapproché'),
        ('cancelled', 'Annulé')
    ], default='draft', string='Statut', tracking=True)
    
    # Mainlevée
    mainlevee_reference = fields.Char('Référence de Mainlevée', related='bordereau_id.mainlevee_reference', store=True)
    mainlevee_linked = fields.Boolean('Mainlevée liée', compute='_compute_mainlevee_linked', store=True)
    
    # Audit trail
    created_by = fields.Many2one('res.users', 'Créé par', default=lambda self: self.env.user, readonly=True)
    validated_by = fields.Many2one('res.users', 'Validé par')
    validation_date = fields.Datetime('Date de validation')
    
    # Lettrage multiple (pour les paiements partiels)
    parent_lettrage_id = fields.Many2one('tresorerie.lettrage', 'Lettrage parent')
    child_lettrage_ids = fields.One2many('tresorerie.lettrage', 'parent_lettrage_id', 'Lettrages enfants')
    is_parent = fields.Boolean('Lettrage parent', compute='_compute_is_parent', store=True)
    
    # Informations complémentaires
    vessel_name = fields.Char('Nom du navire', related='payment_id.port_fee_id.vessel_name', store=True)
    port_fee_id = fields.Many2one('caisse.port.fee', 'Redevance Portuaire', related='payment_id.port_fee_id', store=True)
    
    @api.depends('invoice_amount', 'amount')
    def _compute_remaining_amount(self):
        for lettrage in self:
            lettrage.remaining_amount = lettrage.invoice_amount - lettrage.amount
    
    @api.depends('remaining_amount')
    def _compute_is_complete(self):
        for lettrage in self:
            lettrage.is_complete = lettrage.remaining_amount == 0
    
    @api.depends('mainlevee_reference')
    def _compute_mainlevee_linked(self):
        for lettrage in self:
            lettrage.mainlevee_linked = bool(lettrage.mainlevee_reference)
    
    @api.depends('child_lettrage_ids')
    def _compute_is_parent(self):
        for lettrage in self:
            lettrage.is_parent = len(lettrage.child_lettrage_ids) > 0
    
    @api.model
    def create(self, vals):
        if vals.get('name', 'Nouveau') == 'Nouveau':
            sequence = self.env['ir.sequence'].next_by_code('tresorerie.lettrage')
            vals['name'] = sequence or 'LET'
        
        # Automatiquement activer le lettrage à la création
        vals['state'] = 'active'
        
        return super().create(vals)
    
    @api.onchange('payment_id')
    def _onchange_payment_id(self):
        if self.payment_id:
            self.invoice_id = self.payment_id.invoice_id
            self.bordereau_id = self.payment_id.bordereau_id
            self.amount = self.payment_id.amount_paid
            self.is_partial = self.payment_id.is_partial_payment
    
    def action_validate_lettrage(self):
        """Valider le lettrage"""
        self.ensure_one()
        
        if self.state != 'draft':
            raise UserError(_('Seuls les lettrages en brouillon peuvent être validés.'))
        
        # Vérifications
        if self.amount <= 0:
            raise UserError(_('Le montant lettré doit être positif.'))
        
        if self.amount > self.invoice_amount:
            raise UserError(_('Le montant lettré ne peut pas dépasser le montant de la facture.'))
        
        if self.amount > self.payment_amount:
            raise UserError(_('Le montant lettré ne peut pas dépasser le montant payé.'))
        
        self.state = 'active'
        self.validated_by = self.env.user
        self.validation_date = fields.Datetime.now()
        
        # Si lettrage complet, marquer comme rapproché
        if self.is_complete:
            self.state = 'reconciled'
            
            # Marquer la facture comme payée si pas déjà fait
            if self.invoice_id.payment_state != 'paid':
                self.invoice_id.payment_state = 'paid'
        
        self.message_post(
            body=_("Lettrage validé par %s") % self.env.user.name,
            message_type='notification'
        )
    
    def action_cancel_lettrage(self):
        """Annuler le lettrage"""
        self.ensure_one()
        
        # Vérifier les permissions
        if not self.env.user.has_group('tresorie.group_treasury_chief'):
            raise UserError(_('Seul le chef de trésorerie peut annuler un lettrage.'))
        
        if self.state == 'cancelled':
            raise UserError(_('Le lettrage est déjà annulé.'))
        
        self.state = 'cancelled'
        
        # Si c'était un lettrage complet, remettre la facture en état non payé
        if self.is_complete and self.invoice_id.payment_state == 'paid':
            self.invoice_id.payment_state = 'not_paid'
        
        self.message_post(
            body=_("Lettrage annulé par %s") % self.env.user.name,
            message_type='notification'
        )
    
    def action_create_partial_lettrage(self):
        """Créer un lettrage partiel supplémentaire"""
        self.ensure_one()
        
        if self.is_complete:
            raise UserError(_('Impossible de créer un lettrage partiel pour une facture déjà complètement lettrée.'))
        
        remaining = self.remaining_amount
        if remaining <= 0:
            raise UserError(_('Aucun montant restant à lettrer.'))
        
        # Ouvrir un wizard pour saisir le nouveau paiement partiel
        return {
            'type': 'ir.actions.act_window',
            'name': _('Nouveau paiement partiel'),
            'res_model': 'tresorerie.partial.payment.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_lettrage_id': self.id,
                'default_invoice_id': self.invoice_id.id,
                'default_remaining_amount': remaining,
            }
        }
    
    def action_view_related_documents(self):
        """Voir les documents liés"""
        self.ensure_one()
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Documents liés - %s') % self.name,
            'res_model': 'tresorerie.lettrage.documents.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_lettrage_id': self.id,
            }
        }
    
    def get_lettrage_summary(self):
        """Obtenir un résumé du lettrage"""
        self.ensure_one()
        
        summary = {
            'lettrage_ref': self.name,
            'partner_name': self.partner_name,
            'invoice_number': self.invoice_id.name,
            'payment_number': self.payment_id.name,
            'bordereau_number': self.bordereau_id.name,
            'amount_lettre': self.amount,
            'remaining_amount': self.remaining_amount,
            'is_complete': self.is_complete,
            'is_partial': self.is_partial,
            'mainlevee_reference': self.mainlevee_reference,
            'vessel_name': self.vessel_name,
            'state': self.state,
            'validation_date': self.validation_date,
        }
        
        return summary
    
    @api.model
    def get_lettrage_statistics(self, date_from=None, date_to=None):
        """Obtenir des statistiques sur les lettrages"""
        domain = [('state', '!=', 'cancelled')]
        
        if date_from:
            domain.append(('date', '>=', date_from))
        if date_to:
            domain.append(('date', '<=', date_to))
        
        lettrages = self.search(domain)
        
        stats = {
            'total_lettrages': len(lettrages),
            'lettrages_complets': len(lettrages.filtered('is_complete')),
            'lettrages_partiels': len(lettrages.filtered('is_partial')),
            'montant_total_lettre': sum(lettrages.mapped('amount')),
            'montant_restant_total': sum(lettrages.mapped('remaining_amount')),
            'avec_mainlevee': len(lettrages.filtered('mainlevee_linked')),
        }
        
        return stats
    
    def name_get(self):
        result = []
        for record in self:
            name = f"{record.name}"
            if record.is_partial:
                name += " (Partiel)"
            if record.is_complete:
                name += " (Complet)"
            if record.state == 'cancelled':
                name += " (ANNULÉ)"
            result.append((record.id, name))
        return result
    
    @api.constrains('amount', 'invoice_amount', 'payment_amount')
    def _check_amounts(self):
        for lettrage in self:
            if lettrage.amount <= 0:
                raise ValidationError(_('Le montant lettré doit être positif.'))
            
            if lettrage.amount > lettrage.invoice_amount:
                raise ValidationError(_('Le montant lettré ne peut pas dépasser le montant de la facture.'))
            
            if lettrage.amount > lettrage.payment_amount:
                raise ValidationError(_('Le montant lettré ne peut pas dépasser le montant payé.'))
