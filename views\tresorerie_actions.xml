<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Dashboard Actions -->
    <record id="action_tresorerie_dashboard" model="ir.actions.act_window">
        <field name="name">Tableau de Bord Trésorerie</field>
        <field name="res_model">tresorerie.payment</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="domain">[('date', '&gt;=', context_today().strftime('%Y-%m-01'))]</field>
        <field name="context">{
            'search_default_this_month': 1,
            'search_default_group_state': 1
        }</field>
    </record>

    <!-- Collection Tracking Actions -->
    <record id="action_daily_collection" model="ir.actions.act_window">
        <field name="name">Encaissements du Jour</field>
        <field name="res_model">tresorerie.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[
            ('date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))),
            ('date', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59))),
            ('state', '=', 'validated')
        ]</field>
        <field name="context">{'search_default_group_caisse': 1}</field>
    </record>

    <record id="action_weekly_collection" model="ir.actions.act_window">
        <field name="name">Encaissements de la Semaine</field>
        <field name="res_model">tresorerie.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[
            ('date', '&gt;=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')),
            ('state', '=', 'validated')
        ]</field>
        <field name="context">{'search_default_group_date': 1}</field>
    </record>

    <record id="action_monthly_collection" model="ir.actions.act_window">
        <field name="name">Encaissements du Mois</field>
        <field name="res_model">tresorerie.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[
            ('date', '&gt;=', context_today().strftime('%Y-%m-01')),
            ('state', '=', 'validated')
        ]</field>
        <field name="context">{'search_default_group_cashier': 1}</field>
    </record>

    <!-- Validation Workflow Actions -->
    <record id="action_pending_validation" model="ir.actions.act_window">
        <field name="name">En Attente de Validation</field>
        <field name="res_model">tresorerie.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'pending')]</field>
        <field name="context">{'search_default_group_cashier': 1}</field>
    </record>

    <record id="action_bordereau_to_sign" model="ir.actions.act_window">
        <field name="name">Bordereaux à Signer</field>
        <field name="res_model">tresorerie.bordereau</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'generated')]</field>
    </record>

    <record id="action_documents_to_verify" model="ir.actions.act_window">
        <field name="name">Documents à Vérifier</field>
        <field name="res_model">tresorerie.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('all_documents_verified', '=', False), ('state', '=', 'draft')]</field>
    </record>

    <!-- Payment History Actions -->
    <record id="action_payment_history" model="ir.actions.act_window">
        <field name="name">Historique des Paiements</field>
        <field name="res_model">tresorerie.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{
            'search_default_validated': 1,
            'search_default_group_date': 1
        }</field>
    </record>

    <record id="action_payment_by_partner" model="ir.actions.act_window">
        <field name="name">Paiements par Redevable</field>
        <field name="res_model">tresorerie.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{
            'search_default_validated': 1,
            'search_default_group_partner': 1
        }</field>
    </record>

    <!-- Lettrage Actions -->
    <record id="action_automatic_lettrage" model="ir.actions.server">
        <field name="name">Lettrage Automatique</field>
        <field name="model_id" ref="base.model_tresorerie_lettrage"/>
        <field name="state">code</field>
        <field name="code">
# Automatic lettrage for unmatched payments
unmatched_payments = env['tresorerie.payment'].search([
    ('state', '=', 'validated'),
    ('lettrage_id', '=', False)
])
for payment in unmatched_payments:
    payment.create_lettrage()
        </field>
    </record>

    <!-- Search and Filter Actions -->
    <record id="action_advanced_search" model="ir.actions.act_window">
        <field name="name">Recherche Avancée</field>
        <field name="res_model">tresorerie.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_tresorerie_payment_search"/>
    </record>

    <record id="action_search_by_vessel" model="ir.actions.act_window">
        <field name="name">Recherche par Navire</field>
        <field name="res_model">tresorerie.bordereau</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_bordereau_search"/>
    </record>

    <record id="action_search_by_mainlevee" model="ir.actions.act_window">
        <field name="name">Recherche par Mainlevée</field>
        <field name="res_model">tresorerie.lettrage</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('mainlevee_linked', '=', True)]</field>
    </record>

    <!-- Reporting Actions -->
    <record id="action_treasury_report" model="ir.actions.act_window">
        <field name="name">Rapport Trésorerie</field>
        <field name="res_model">tresorerie.payment</field>
        <field name="view_mode">graph,pivot,tree</field>
        <field name="context">{
            'search_default_validated': 1,
            'search_default_this_month': 1,
            'group_by': ['date', 'caisse_id', 'payment_method']
        }</field>
    </record>

    <record id="action_collection_report" model="ir.actions.act_window">
        <field name="name">Rapport d'Encaissement</field>
        <field name="res_model">tresorerie.payment</field>
        <field name="view_mode">pivot,graph,tree</field>
        <field name="domain">[('state', '=', 'validated')]</field>
        <field name="context">{
            'group_by': ['cashier_id', 'caisse_id', 'date'],
            'measures': ['amount_paid']
        }</field>
    </record>

    <record id="action_refund_report" model="ir.actions.act_window">
        <field name="name">Rapport des Remboursements</field>
        <field name="res_model">tresorerie.refund</field>
        <field name="view_mode">pivot,graph,tree</field>
        <field name="domain">[('state', '=', 'processed')]</field>
        <field name="context">{
            'group_by': ['reason', 'refund_method', 'date'],
            'measures': ['refund_amount']
        }</field>
    </record>

    <!-- Archive Actions -->
    <record id="action_archive_receipts" model="ir.actions.server">
        <field name="name">Archiver les Reçus</field>
        <field name="model_id" ref="base.model_tresorerie_receipt"/>
        <field name="state">code</field>
        <field name="code">
# Archive receipts older than 30 days
from datetime import datetime, timedelta
cutoff_date = datetime.now() - timedelta(days=30)
old_receipts = env['tresorerie.receipt'].search([
    ('date', '&lt;', cutoff_date),
    ('archived', '=', False),
    ('state', '!=', 'cancelled')
])
for receipt in old_receipts:
    receipt.action_archive_receipt()
        </field>
    </record>

    <!-- Notification Actions -->
    <record id="action_notify_pending_validation" model="ir.actions.server">
        <field name="name">Notifier Validations Pendantes</field>
        <field name="model_id" ref="base.model_tresorerie_payment"/>
        <field name="state">code</field>
        <field name="code">
# Notify treasury chiefs of pending validations
pending_payments = env['tresorerie.payment'].search([('state', '=', 'pending')])
if pending_payments:
    treasury_chiefs = env['res.users'].search([('groups_id', 'in', [env.ref('tresorie.group_treasury_chief').id])])
    for chief in treasury_chiefs:
        env['mail.activity'].create({
            'activity_type_id': env.ref('mail.mail_activity_data_todo').id,
            'summary': f'{len(pending_payments)} paiement(s) en attente de validation',
            'note': f'Il y a {len(pending_payments)} paiement(s) en attente de votre validation.',
            'res_id': pending_payments[0].id,
            'res_model_id': env['ir.model']._get('tresorerie.payment').id,
            'user_id': chief.id,
        })
        </field>
    </record>

    <!-- Wizard Actions -->
    <record id="action_reject_payment_wizard" model="ir.actions.act_window">
        <field name="name">Rejeter le Paiement</field>
        <field name="res_model">tresorerie.payment.reject.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <record id="action_partial_payment_wizard" model="ir.actions.act_window">
        <field name="name">Paiement Partiel</field>
        <field name="res_model">tresorerie.partial.payment.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Export Actions -->
    <record id="action_export_daily_collection" model="ir.actions.server">
        <field name="name">Exporter Encaissements du Jour</field>
        <field name="model_id" ref="base.model_tresorerie_payment"/>
        <field name="state">code</field>
        <field name="code">
# Export daily collection to Excel
action = {
    'type': 'ir.actions.act_url',
    'url': '/web/export/xlsx?model=tresorerie.payment&amp;fields=name,date,partner_name,amount_paid,payment_method,cashier_id&amp;domain=[("date","&gt;=","%s"),("date","&lt;=","%s"),("state","=","validated")]' % (
        fields.Date.today().strftime('%Y-%m-%d'),
        fields.Date.today().strftime('%Y-%m-%d')
    ),
    'target': 'self',
}
        </field>
    </record>

    <!-- Cron Actions -->
    <record id="cron_daily_collection_summary" model="ir.cron">
        <field name="name">Résumé Quotidien des Encaissements</field>
        <field name="model_id" ref="base.model_tresorerie_payment"/>
        <field name="state">code</field>
        <field name="code">model.send_daily_collection_summary()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="active">True</field>
    </record>

    <record id="cron_auto_archive_receipts" model="ir.cron">
        <field name="name">Archivage Automatique des Reçus</field>
        <field name="model_id" ref="base.model_tresorerie_receipt"/>
        <field name="state">code</field>
        <field name="code">model.auto_archive_old_receipts()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">weeks</field>
        <field name="numbercall">-1</field>
        <field name="active">True</field>
    </record>
</odoo>
