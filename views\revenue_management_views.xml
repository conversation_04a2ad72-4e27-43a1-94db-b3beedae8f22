<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Revenue Category Views -->
    <record id="view_revenue_category_tree" model="ir.ui.view">
        <field name="name">caisse.revenue.category.tree</field>
        <field name="model">caisse.revenue.category</field>
        <field name="arch" type="xml">
            <tree string="Catégories de Recettes">
                <field name="name"/>
                <field name="code"/>
                <field name="account_id"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <record id="view_revenue_category_form" model="ir.ui.view">
        <field name="name">caisse.revenue.category.form</field>
        <field name="model">caisse.revenue.category</field>
        <field name="arch" type="xml">
            <form string="Catégorie de Recettes">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="code"/>
                        <field name="account_id"/>
                        <field name="active"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Revenue Collection Views -->
    <record id="view_revenue_collection_tree" model="ir.ui.view">
        <field name="name">caisse.revenue.collection.tree</field>
        <field name="model">caisse.revenue.collection</field>
        <field name="arch" type="xml">
            <tree string="Régies de Recettes">
                <field name="name"/>
                <field name="collector_id"/>
                <field name="caisse_id"/>
                <field name="total_amount"/>
                <field name="date"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="view_revenue_collection_form" model="ir.ui.view">
        <field name="name">caisse.revenue.collection.form</field>
        <field name="model">caisse.revenue.collection</field>
        <field name="arch" type="xml">
            <form string="Régie de Recettes">
                <header>
                    <button name="validate_collection" type="object" string="Valider" 
                            class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="deposit_collection" type="object" string="Verser" 
                            class="btn-primary" attrs="{'invisible': [('state', '!=', 'validated')]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,validated,deposited"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="date"/>
                        </group>
                        <group>
                            <field name="collector_id"/>
                            <field name="caisse_id"/>
                            <field name="total_amount"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Lignes de Recettes">
                            <field name="line_ids">
                                <tree editable="bottom">
                                    <field name="category_id"/>
                                    <field name="description"/>
                                    <field name="amount"/>
                                    <field name="partner_id"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_revenue_category" model="ir.actions.act_window">
        <field name="name">Catégories de Recettes</field>
        <field name="res_model">caisse.revenue.category</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record id="action_revenue_collection" model="ir.actions.act_window">
        <field name="name">Régies de Recettes</field>
        <field name="res_model">caisse.revenue.collection</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>