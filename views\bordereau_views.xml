<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Bordereau Tree View -->
    <record id="view_bordereau_tree" model="ir.ui.view">
        <field name="name">tresorerie.bordereau.tree</field>
        <field name="model">tresorerie.bordereau</field>
        <field name="arch" type="xml">
            <tree string="Bordereaux de Versement" decoration-info="state=='draft'" decoration-success="state=='signed'" decoration-warning="state=='generated'" decoration-danger="state=='cancelled'">
                <field name="name"/>
                <field name="date"/>
                <field name="partner_name"/>
                <field name="vessel_name"/>
                <field name="amount" widget="monetary"/>
                <field name="state" widget="badge"/>
                <field name="receiver_id"/>
                <field name="signature_date"/>
            </tree>
        </field>
    </record>

    <!-- Bordereau Form View -->
    <record id="view_bordereau_form" model="ir.ui.view">
        <field name="name">tresorerie.bordereau.form</field>
        <field name="model">tresorerie.bordereau</field>
        <field name="arch" type="xml">
            <form string="Bordereau de Versement">
                <header>
                    <button name="action_generate" type="object" string="Générer" class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="action_sign" type="object" string="Signer" class="btn-success" attrs="{'invisible': [('state', '!=', 'generated')]}"/>
                    <button name="action_cancel" type="object" string="Annuler" attrs="{'invisible': [('state', 'in', ['paid', 'cancelled'])]}"/>
                    <button name="action_reset_to_draft" type="object" string="Remettre en brouillon" attrs="{'invisible': [('state', 'in', ['draft', 'paid'])]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,generated,signed,paid"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_payment" type="object" class="oe_stat_button" icon="fa-money" attrs="{'invisible': [('payment_id', '=', False)]}">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Paiement</span>
                            </div>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="date"/>
                            <field name="port_fee_id" options="{'no_create': True}"/>
                            <field name="invoice_id" options="{'no_create': True}"/>
                            <field name="mainlevee_reference"/>
                        </group>
                        <group>
                            <field name="receiver_id"/>
                            <field name="signature_date" readonly="1"/>
                            <field name="signed_by" readonly="1"/>
                            <field name="payment_date" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Informations Redevable">
                            <group>
                                <group>
                                    <field name="partner_id" options="{'no_create': True}"/>
                                    <field name="partner_name" readonly="1"/>
                                    <field name="partner_nui" readonly="1"/>
                                </group>
                                <group>
                                    <field name="amount" widget="monetary"/>
                                    <field name="currency_id" invisible="1"/>
                                    <field name="company_id" invisible="1"/>
                                </group>
                            </group>
                            <group>
                                <field name="partner_address" readonly="1"/>
                            </group>
                        </page>
                        
                        <page string="Détails Navire">
                            <group>
                                <group>
                                    <field name="vessel_name" readonly="1"/>
                                    <field name="vessel_type" readonly="1"/>
                                </group>
                                <group>
                                    <field name="tonnage" readonly="1"/>
                                </group>
                            </group>
                            <group>
                                <field name="description"/>
                            </group>
                        </page>
                        
                        <page string="Audit">
                            <group>
                                <group>
                                    <field name="created_by" readonly="1"/>
                                    <field name="created_date" readonly="1"/>
                                </group>
                                <group>
                                    <field name="payment_id" readonly="1" attrs="{'invisible': [('payment_id', '=', False)]}"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Bordereau Search View -->
    <record id="view_bordereau_search" model="ir.ui.view">
        <field name="name">tresorerie.bordereau.search</field>
        <field name="model">tresorerie.bordereau</field>
        <field name="arch" type="xml">
            <search string="Rechercher Bordereaux">
                <field name="name" string="Numéro"/>
                <field name="partner_name" string="Redevable"/>
                <field name="vessel_name" string="Navire"/>
                <field name="mainlevee_reference" string="Mainlevée"/>
                <field name="receiver_id" string="Receveur"/>
                
                <filter name="draft" string="Brouillon" domain="[('state', '=', 'draft')]"/>
                <filter name="generated" string="Généré" domain="[('state', '=', 'generated')]"/>
                <filter name="signed" string="Signé" domain="[('state', '=', 'signed')]"/>
                <filter name="paid" string="Payé" domain="[('state', '=', 'paid')]"/>
                <filter name="cancelled" string="Annulé" domain="[('state', '=', 'cancelled')]"/>
                
                <separator/>
                <filter name="today" string="Aujourd'hui" domain="[('date', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter name="this_week" string="Cette semaine" domain="[('date', '&gt;=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                <filter name="this_month" string="Ce mois" domain="[('date', '&gt;=', context_today().strftime('%Y-%m-01'))]"/>
                
                <group expand="0" string="Grouper par">
                    <filter name="group_state" string="Statut" context="{'group_by': 'state'}"/>
                    <filter name="group_partner" string="Redevable" context="{'group_by': 'partner_id'}"/>
                    <filter name="group_date" string="Date" context="{'group_by': 'date'}"/>
                    <filter name="group_receiver" string="Receveur" context="{'group_by': 'receiver_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Bordereau Kanban View -->
    <record id="view_bordereau_kanban" model="ir.ui.view">
        <field name="name">tresorerie.bordereau.kanban</field>
        <field name="model">tresorerie.bordereau</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" class="o_kanban_small_column">
                <field name="name"/>
                <field name="partner_name"/>
                <field name="vessel_name"/>
                <field name="amount"/>
                <field name="date"/>
                <field name="state"/>
                <field name="currency_id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div><strong><field name="partner_name"/></strong></div>
                                    <div><field name="vessel_name"/></div>
                                    <div class="text-muted">
                                        <field name="date"/>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_record_bottom">
                                <div class="oe_kanban_bottom_left">
                                    <field name="amount" widget="monetary"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_bordereau" model="ir.actions.act_window">
        <field name="name">Bordereaux de Versement</field>
        <field name="res_model">tresorerie.bordereau</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="context">{'search_default_this_month': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Créer votre premier bordereau de versement
            </p>
            <p>
                Les bordereaux de versement sont générés automatiquement pour les redevances portuaires
                et doivent être signés avant encaissement.
            </p>
        </field>
    </record>

    <!-- Action for pending bordereaux -->
    <record id="action_bordereau_pending" model="ir.actions.act_window">
        <field name="name">Bordereaux en Attente</field>
        <field name="res_model">tresorerie.bordereau</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', 'in', ['draft', 'generated'])]</field>
        <field name="context">{'search_default_draft': 1, 'search_default_generated': 1}</field>
    </record>

    <!-- Action for signed bordereaux -->
    <record id="action_bordereau_signed" model="ir.actions.act_window">
        <field name="name">Bordereaux Signés</field>
        <field name="res_model">tresorerie.bordereau</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'signed')]</field>
    </record>
</odoo>
