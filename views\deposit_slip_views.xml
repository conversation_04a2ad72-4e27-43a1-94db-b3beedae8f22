<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Deposit Slip Tree View -->
    <record id="view_deposit_slip_tree" model="ir.ui.view">
        <field name="name">caisse.deposit.slip.tree</field>
        <field name="model">caisse.deposit.slip</field>
        <field name="arch" type="xml">
            <tree string="Bordereaux de Versement">
                <field name="name"/>
                <field name="caisse_id"/>
                <field name="bank_account_id"/>
                <field name="total_amount"/>
                <field name="date"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Deposit Slip Form View -->
    <record id="view_deposit_slip_form" model="ir.ui.view">
        <field name="name">caisse.deposit.slip.form</field>
        <field name="model">caisse.deposit.slip</field>
        <field name="arch" type="xml">
            <form string="Bordereau de Versement">
                <header>
                    <button name="confirm_slip" type="object" string="Confirmer" 
                            class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="register_deposit" type="object" string="Enregistrer Dépôt" 
                            class="btn-primary" attrs="{'invisible': [('state', '!=', 'confirmed')]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,confirmed,deposited"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="date"/>
                        </group>
                        <group>
                            <field name="caisse_id"/>
                            <field name="bank_account_id"/>
                            <field name="total_amount"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Lignes de Dépôt">
                            <field name="line_ids">
                                <tree editable="bottom">
                                    <field name="description"/>
                                    <field name="amount"/>
                                    <field name="movement_id"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Deposit Slip Action -->
    <record id="action_deposit_slip" model="ir.actions.act_window">
        <field name="name">Bordereaux de Versement</field>
        <field name="res_model">caisse.deposit.slip</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>