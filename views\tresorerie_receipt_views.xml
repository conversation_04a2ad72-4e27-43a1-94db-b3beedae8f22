<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Receipt Tree View -->
    <record id="view_tresorerie_receipt_tree" model="ir.ui.view">
        <field name="name">tresorerie.receipt.tree</field>
        <field name="model">tresorerie.receipt</field>
        <field name="arch" type="xml">
            <tree string="Reçus de Paiement" decoration-success="state=='issued'" decoration-info="state=='printed'" decoration-danger="state=='cancelled'">
                <field name="name"/>
                <field name="date"/>
                <field name="partner_name"/>
                <field name="amount" widget="monetary"/>
                <field name="payment_method_display"/>
                <field name="state" widget="badge"/>
                <field name="cashier_name"/>
                <field name="print_count"/>
                <field name="archived" widget="boolean_toggle"/>
            </tree>
        </field>
    </record>

    <!-- Receipt Form View -->
    <record id="view_tresorerie_receipt_form" model="ir.ui.view">
        <field name="name">tresorerie.receipt.form</field>
        <field name="model">tresorerie.receipt</field>
        <field name="arch" type="xml">
            <form string="Reçu de Paiement">
                <header>
                    <button name="action_print_receipt" type="object" string="Imprimer" class="btn-primary" attrs="{'invisible': [('state', '=', 'cancelled')]}"/>
                    <button name="action_duplicate_receipt" type="object" string="Duplicata" class="btn-info" attrs="{'invisible': [('state', '=', 'cancelled')]}"/>
                    <button name="action_cancel_receipt" type="object" string="Annuler" class="btn-danger" attrs="{'invisible': [('state', '=', 'cancelled')]}" groups="tresorie.group_treasury_chief"/>
                    <button name="action_archive_receipt" type="object" string="Archiver" class="btn-secondary" attrs="{'invisible': ['|', ('archived', '=', True), ('state', '=', 'cancelled')]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="issued,printed"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_payment" type="object" class="oe_stat_button" icon="fa-money">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Paiement</span>
                            </div>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                        <div attrs="{'invisible': [('archived', '=', False)]}">
                            <span class="badge badge-warning">ARCHIVÉ</span>
                        </div>
                    </div>
                    
                    <group>
                        <group>
                            <field name="date"/>
                            <field name="partner_id" readonly="1"/>
                            <field name="partner_name" readonly="1"/>
                        </group>
                        <group>
                            <field name="amount" widget="monetary"/>
                            <field name="currency_id" invisible="1"/>
                            <field name="company_id" invisible="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Détails Paiement">
                            <group>
                                <group>
                                    <field name="payment_method"/>
                                    <field name="payment_method_display" readonly="1"/>
                                </group>
                                <group>
                                    <field name="caisse_id" readonly="1"/>
                                    <field name="cashier_name" readonly="1"/>
                                </group>
                            </group>
                            
                            <group string="Détails Chèque" attrs="{'invisible': [('payment_method', '!=', 'check')]}">
                                <group>
                                    <field name="check_number" readonly="1"/>
                                    <field name="check_date" readonly="1"/>
                                </group>
                                <group>
                                    <field name="bank_name" readonly="1"/>
                                </group>
                            </group>
                            
                            <group string="Détails Virement" attrs="{'invisible': [('payment_method', '!=', 'transfer')]}">
                                <group>
                                    <field name="transfer_reference" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Références">
                            <group>
                                <group>
                                    <field name="payment_id" readonly="1"/>
                                    <field name="bordereau_id" readonly="1"/>
                                    <field name="invoice_id" readonly="1"/>
                                </group>
                                <group>
                                    <field name="vessel_name" readonly="1"/>
                                    <field name="vessel_type" readonly="1"/>
                                    <field name="tonnage" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Impression">
                            <group>
                                <group>
                                    <field name="print_count" readonly="1"/>
                                    <field name="last_print_date" readonly="1"/>
                                    <field name="last_print_user" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Archivage">
                            <group>
                                <group>
                                    <field name="archived" readonly="1"/>
                                    <field name="archive_date" readonly="1"/>
                                </group>
                                <group>
                                    <field name="archive_location" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Adresse">
                            <group>
                                <field name="partner_address" readonly="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Receipt Search View -->
    <record id="view_tresorerie_receipt_search" model="ir.ui.view">
        <field name="name">tresorerie.receipt.search</field>
        <field name="model">tresorerie.receipt</field>
        <field name="arch" type="xml">
            <search string="Rechercher Reçus">
                <field name="name" string="Numéro"/>
                <field name="partner_name" string="Redevable"/>
                <field name="vessel_name" string="Navire"/>
                <field name="cashier_name" string="Caissier"/>
                <field name="caisse_id" string="Caisse"/>
                
                <filter name="issued" string="Émis" domain="[('state', '=', 'issued')]"/>
                <filter name="printed" string="Imprimé" domain="[('state', '=', 'printed')]"/>
                <filter name="cancelled" string="Annulé" domain="[('state', '=', 'cancelled')]"/>
                
                <separator/>
                <filter name="archived" string="Archivé" domain="[('archived', '=', True)]"/>
                <filter name="not_archived" string="Non Archivé" domain="[('archived', '=', False)]"/>
                
                <separator/>
                <filter name="today" string="Aujourd'hui" domain="[('date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('date', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <filter name="this_week" string="Cette semaine" domain="[('date', '&gt;=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                <filter name="this_month" string="Ce mois" domain="[('date', '&gt;=', context_today().strftime('%Y-%m-01'))]"/>
                
                <group expand="0" string="Grouper par">
                    <filter name="group_state" string="Statut" context="{'group_by': 'state'}"/>
                    <filter name="group_cashier" string="Caissier" context="{'group_by': 'cashier_id'}"/>
                    <filter name="group_caisse" string="Caisse" context="{'group_by': 'caisse_id'}"/>
                    <filter name="group_payment_method" string="Mode de paiement" context="{'group_by': 'payment_method'}"/>
                    <filter name="group_date" string="Date" context="{'group_by': 'date'}"/>
                    <filter name="group_archived" string="Archivage" context="{'group_by': 'archived'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_tresorerie_receipt" model="ir.actions.act_window">
        <field name="name">Reçus de Paiement</field>
        <field name="res_model">tresorerie.receipt</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_not_archived': 1, 'search_default_this_month': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Aucun reçu trouvé
            </p>
            <p>
                Les reçus sont générés automatiquement lors de la validation des paiements.
            </p>
        </field>
    </record>

    <!-- Action for receipts to print -->
    <record id="action_receipt_to_print" model="ir.actions.act_window">
        <field name="name">Reçus à Imprimer</field>
        <field name="res_model">tresorerie.receipt</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'issued')]</field>
    </record>

    <!-- Action for archived receipts -->
    <record id="action_receipt_archived" model="ir.actions.act_window">
        <field name="name">Reçus Archivés</field>
        <field name="res_model">tresorerie.receipt</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('archived', '=', True)]</field>
    </record>
</odoo>
