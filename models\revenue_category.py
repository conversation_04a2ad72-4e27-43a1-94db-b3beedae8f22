from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class RevenueCategory(models.Model):
    _name = 'caisse.revenue.category'
    _description = 'Catégorie de Recettes'
    _order = 'name'

    name = fields.Char('Nom', required=True)
    code = fields.Char('Code', required=True)
    account_id = fields.Many2one('account.account', 'Compte comptable')
    active = fields.Boolean('Actif', default=True)
    description = fields.Text('Description')
    
    company_id = fields.Many2one('res.company', 'Société', default=lambda self: self.env.company)

    @api.constrains('code')
    def _check_code_unique(self):
        for record in self:
            if self.search_count([('code', '=', record.code), ('id', '!=', record.id)]) > 0:
                raise ValidationError(_('Le code de la catégorie doit être unique.'))