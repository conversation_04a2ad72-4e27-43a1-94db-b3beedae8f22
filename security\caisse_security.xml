<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Treasury Security Groups -->
    <record id="group_treasury_cashier" model="res.groups">
        <field name="name">Agent de Cais<PERSON></field>
        <field name="category_id" ref="base.module_category_accounting_accounting"/>
        <field name="comment">Agent de caisse - Encaissement uniquement</field>
    </record>

    <record id="group_treasury_chief" model="res.groups">
        <field name="name">Chef de Trésorerie</field>
        <field name="category_id" ref="base.module_category_accounting_accounting"/>
        <field name="implied_ids" eval="[(4, ref('group_treasury_cashier'))]"/>
        <field name="comment">Chef de trésorerie - Validation et supervision</field>
    </record>

    <record id="group_treasury_controller" model="res.groups">
        <field name="name">Contrôleur Interne</field>
        <field name="category_id" ref="base.module_category_accounting_accounting"/>
        <field name="comment">Contrôleur interne - Audit et accès lecture seule</field>
    </record>

    <!-- Legacy groups for backward compatibility -->
    <record id="group_caisse_collector" model="res.groups">
        <field name="name">Caisse Collector (Legacy)</field>
        <field name="category_id" ref="base.module_category_accounting_accounting"/>
        <field name="implied_ids" eval="[(4, ref('group_treasury_cashier'))]"/>
    </record>

    <record id="group_caisse_validator" model="res.groups">
        <field name="name">Caisse Validator (Legacy)</field>
        <field name="category_id" ref="base.module_category_accounting_accounting"/>
        <field name="implied_ids" eval="[(4, ref('group_treasury_chief'))]"/>
    </record>

    <record id="group_caisse_controller" model="res.groups">
        <field name="name">Caisse Controller (Legacy)</field>
        <field name="category_id" ref="base.module_category_accounting_accounting"/>
        <field name="implied_ids" eval="[(4, ref('group_treasury_controller'))]"/>
    </record>
    
    <!-- Record Rules for Treasury Models -->

    <!-- Bordereau Rules -->
    <record id="rule_bordereau_cashier" model="ir.rule">
        <field name="name">Bordereau: Cashier can see all</field>
        <field name="model_id" ref="model_tresorerie_bordereau"/>
        <field name="groups" eval="[(4, ref('group_treasury_cashier'))]"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>

    <record id="rule_bordereau_controller" model="ir.rule">
        <field name="name">Bordereau: Controller read-only access</field>
        <field name="model_id" ref="model_tresorerie_bordereau"/>
        <field name="groups" eval="[(4, ref('group_treasury_controller'))]"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- Payment Rules -->
    <record id="rule_payment_cashier" model="ir.rule">
        <field name="name">Payment: Cashier can see own payments</field>
        <field name="model_id" ref="model_tresorerie_payment"/>
        <field name="groups" eval="[(4, ref('group_treasury_cashier'))]"/>
        <field name="domain_force">[('cashier_id', '=', user.id)]</field>
    </record>

    <record id="rule_payment_chief" model="ir.rule">
        <field name="name">Payment: Chief can see all payments</field>
        <field name="model_id" ref="model_tresorerie_payment"/>
        <field name="groups" eval="[(4, ref('group_treasury_chief'))]"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>

    <record id="rule_payment_controller" model="ir.rule">
        <field name="name">Payment: Controller read-only access</field>
        <field name="model_id" ref="model_tresorerie_payment"/>
        <field name="groups" eval="[(4, ref('group_treasury_controller'))]"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- Receipt Rules -->
    <record id="rule_receipt_cashier" model="ir.rule">
        <field name="name">Receipt: Cashier can see own receipts</field>
        <field name="model_id" ref="model_tresorerie_receipt"/>
        <field name="groups" eval="[(4, ref('group_treasury_cashier'))]"/>
        <field name="domain_force">[('cashier_id', '=', user.id)]</field>
    </record>

    <record id="rule_receipt_chief" model="ir.rule">
        <field name="name">Receipt: Chief can see all receipts</field>
        <field name="model_id" ref="model_tresorerie_receipt"/>
        <field name="groups" eval="[(4, ref('group_treasury_chief'))]"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>

    <!-- Refund Rules -->
    <record id="rule_refund_cashier" model="ir.rule">
        <field name="name">Refund: Cashier can see own refunds</field>
        <field name="model_id" ref="model_tresorerie_refund"/>
        <field name="groups" eval="[(4, ref('group_treasury_cashier'))]"/>
        <field name="domain_force">[('requested_by', '=', user.id)]</field>
    </record>

    <record id="rule_refund_chief" model="ir.rule">
        <field name="name">Refund: Chief can see all refunds</field>
        <field name="model_id" ref="model_tresorerie_refund"/>
        <field name="groups" eval="[(4, ref('group_treasury_chief'))]"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>

    <!-- Legacy Rule -->
    <record id="rule_caisse_movement_collector" model="ir.rule">
        <field name="name">Caisse Movement: Collector can only see own movements</field>
        <field name="model_id" ref="model_caisse_movement"/>
        <field name="groups" eval="[(4, ref('group_caisse_collector'))]"/>
        <field name="domain_force">[('create_uid', '=', user.id)]</field>
    </record>
</odoo>



