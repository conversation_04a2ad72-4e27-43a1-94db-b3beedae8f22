from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

class RevenueCollection(models.Model):
    _name = 'caisse.revenue.collection'
    _description = 'Régie de Recettes'
    _order = 'date desc'

    name = fields.Char('Référence', required=True, default='Nouveau')
    collector_id = fields.Many2one('res.users', 'Collecteur', required=True)
    
    company_id = fields.Many2one('res.company', 'Société', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', 'Devise', related='company_id.currency_id')
    
    total_amount = fields.Monetary('Montant total', required=True, currency_field='currency_id')
    date = fields.Date('Date', required=True, default=fields.Date.today)
    description = fields.Text('Description')
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('collected', 'Collecté'),
        ('deposited', 'Déposé')
    ], default='draft')
    
    caisse_id = fields.Many2one('caisse.caisse', 'Caisse')

    @api.model
    def create(self, vals):
        if vals.get('name', 'Nouveau') == 'Nouveau':
            sequence = self.env['ir.sequence'].next_by_code('caisse.revenue.collection')
            vals['name'] = sequence or 'REC'
        return super().create(vals)