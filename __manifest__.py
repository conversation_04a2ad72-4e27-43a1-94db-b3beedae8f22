{
    'name': 'Trésorerie',
    'version': '********.0',
    'summary': 'Module complet de gestion de trésorerie portuaire',
    'description': """
        Module complet de gestion de trésorerie pour les redevances portuaires.

        Fonctionnalités principales:
        - Génération automatique de bordereaux de versement
        - Encaissement des paiements (espèces, chèque, virement)
        - Contrôle des pièces avant paiement
        - Validation par le chef de trésorerie
        - Édition automatique de reçus numérotés
        - Historique et traçabilité complète
        - Lettrage automatique des paiements
        - Suivi des encaissements quotidiens
        - Gestion des remboursements
        - Archivage électronique
        - Recherche avancée multi-critères
        - Alertes et notifications
        - Rapports et statistiques détaillés
        - Sécurité et habilitations par rôles
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'category': 'Accounting',
    'depends': ['base', 'account'],
    'data': [
        # Security (FIRST!)
        'security/caisse_security.xml',
        'security/ir.model.access.csv',
        # Data
        'data/caisse_data.xml',
        'data/caisse_sequences.xml',
        # Views (order matters!)
        'views/caisse_views.xml',
        'views/caisse_session_views.xml',
        'views/caisse_movement_views.xml',
        'views/caisse_transfer_views.xml',
        'views/port_fee_views.xml',
        # New Treasury Views
        'views/bordereau_views.xml',
        'views/tresorerie_payment_views.xml',
        'views/tresorerie_receipt_views.xml',
        'views/tresorerie_lettrage_views.xml',
        'views/tresorerie_refund_views.xml',
        # Wizard Views
        'wizard/wizard_views.xml',
        # Actions
        'views/caisse_action.xml',
        'views/tresorerie_actions.xml',
        # Menu (AFTER all actions are defined)
        'views/caisse_menu.xml',
        # Reports (LAST!)
        'reports/caisse_reports.xml',
        'reports/tresorerie_reports.xml',
    ],
    'demo': [],
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
}




