<?xml version="1.0" encoding="utf-8"?>
<odoo>
	<record id="action_create_new_caisse" model="ir.actions.act_window">
			<field name="name">Nouvelle Caisse</field>
			<field name="res_model">caisse.caisse</field>
			<field name="view_mode">form</field>
			<field name="target">current</field>
			<field name="context">{}</field>
		</record>

	<menuitem id="menu_create_caisse" name="Créer une Caisse"
              parent="base.menu_custom"
              action="action_create_new_caisse"/>	

    <!-- Caisse Tree View -->
    <record id="view_caisse_tree" model="ir.ui.view">
        <field name="name">caisse.caisse.tree</field>
        <field name="model">caisse.caisse</field>
        <field name="arch" type="xml">
            <tree string="Caisses" create="true" edit="true" delete="true">
                <field name="name"/>
                <field name="code"/>
                <field name="responsible_id"/>
                <field name="balance" sum="Total"/>
                <field name="current_session_id"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Caisse Form View -->
    <record id="view_caisse_form" model="ir.ui.view">
        <field name="name">caisse.caisse.form</field>
        <field name="model">caisse.caisse</field>
        <field name="arch" type="xml">
            <form string="Caisse">
                <header>
                    <button name="open_session" type="object" string="Ouvrir Session" 
                            class="btn-primary" attrs="{'invisible': [('current_session_id', '!=', False)]}"/>
                    <button name="close_current_session" type="object" string="Fermer Session" 
                            class="btn-secondary" attrs="{'invisible': [('current_session_id', '=', False)]}"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button class="oe_stat_button" type="object" name="action_view_movements" icon="fa-exchange">
                            <field string="Mouvements" name="movement_ids" widget="statinfo"/>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="code"/>
                            <field name="responsible_id"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="company_id"/>
                            <field name="currency_id"/>
                            <field name="journal_id"/>
                            <field name="current_session_id" readonly="1"/>
                        </group>
                    </group>
                    <group string="Soldes">
                        <group>
                            <field name="balance"/>
                            <field name="theoretical_balance"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Caisse Search View -->
    <record id="view_caisse_search" model="ir.ui.view">
        <field name="name">caisse.caisse.search</field>
        <field name="model">caisse.caisse</field>
        <field name="arch" type="xml">
            <search string="Caisses">
                <field name="name"/>
                <field name="code"/>
                <field name="responsible_id"/>
                <filter string="Actives" name="active" domain="[('active', '=', True)]"/>
                <filter string="Avec session ouverte" name="with_session" domain="[('current_session_id', '!=', False)]"/>
                <group expand="0" string="Grouper par">
                    <filter string="Responsable" name="group_responsible" context="{'group_by': 'responsible_id'}"/>
                    <filter string="Société" name="group_company" context="{'group_by': 'company_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Caisse Action -->
    <record id="action_caisse" model="ir.actions.act_window">
        <field name="name">Caisses</field>
        <field name="res_model">caisse.caisse</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_caisse_search"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Créer votre première caisse
            </p>
            <p>
                Les caisses permettent de gérer les mouvements d'argent liquide.
                Cliquez sur "Créer" pour ajouter une nouvelle caisse.
            </p>
        </field>
    </record>
</odoo>




