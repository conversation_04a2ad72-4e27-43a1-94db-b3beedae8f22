<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Payment Tree View -->
    <record id="view_tresorerie_payment_tree" model="ir.ui.view">
        <field name="name">tresorerie.payment.tree</field>
        <field name="model">tresorerie.payment</field>
        <field name="arch" type="xml">
            <tree string="Paiements Trésorerie" decoration-info="state=='draft'" decoration-warning="state=='pending'" decoration-success="state=='validated'" decoration-danger="state=='rejected'">
                <field name="name"/>
                <field name="date"/>
                <field name="partner_name"/>
                <field name="amount_paid" widget="monetary"/>
                <field name="payment_method"/>
                <field name="state" widget="badge"/>
                <field name="cashier_id"/>
                <field name="validated_by"/>
                <field name="caisse_id"/>
            </tree>
        </field>
    </record>

    <!-- Payment Form View -->
    <record id="view_tresorerie_payment_form" model="ir.ui.view">
        <field name="name">tresorerie.payment.form</field>
        <field name="model">tresorerie.payment</field>
        <field name="arch" type="xml">
            <form string="Paiement Trésorerie">
                <header>
                    <button name="action_verify_documents" type="object" string="Vérifier Documents" class="btn-info" attrs="{'invisible': [('all_documents_verified', '=', True)]}"/>
                    <button name="action_submit_for_validation" type="object" string="Soumettre pour Validation" class="btn-primary" attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('all_documents_verified', '=', False)]}"/>
                    <button name="action_validate" type="object" string="Valider" class="btn-success" attrs="{'invisible': [('state', '!=', 'pending')]}" groups="tresorie.group_treasury_chief"/>
                    <button name="action_reject" type="object" string="Rejeter" class="btn-danger" attrs="{'invisible': [('state', '!=', 'pending')]}" groups="tresorie.group_treasury_chief"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,pending,validated"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_receipt" type="object" class="oe_stat_button" icon="fa-file-text-o" attrs="{'invisible': [('receipt_id', '=', False)]}">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Reçu</span>
                            </div>
                        </button>
                        <button name="action_view_lettrage" type="object" class="oe_stat_button" icon="fa-link" attrs="{'invisible': [('lettrage_id', '=', False)]}">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Lettrage</span>
                            </div>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <!-- Document Verification Alert -->
                    <div class="alert alert-warning" role="alert" attrs="{'invisible': [('all_documents_verified', '=', True)]}">
                        <strong>Attention!</strong> Tous les documents doivent être vérifiés avant soumission.
                    </div>
                    
                    <group>
                        <group>
                            <field name="date"/>
                            <field name="bordereau_id" options="{'no_create': True}" domain="[('state', '=', 'signed')]"/>
                            <field name="invoice_id" options="{'no_create': True}"/>
                            <field name="partner_id" readonly="1"/>
                        </group>
                        <group>
                            <field name="caisse_id" options="{'no_create': True}"/>
                            <field name="session_id" readonly="1"/>
                            <field name="cashier_id" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Montants">
                            <group>
                                <group>
                                    <field name="amount_due" widget="monetary" readonly="1"/>
                                    <field name="amount_paid" widget="monetary"/>
                                    <field name="amount_remaining" widget="monetary" readonly="1"/>
                                    <field name="currency_id" invisible="1"/>
                                    <field name="company_id" invisible="1"/>
                                </group>
                                <group>
                                    <field name="is_partial_payment" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Mode de Paiement">
                            <group>
                                <group>
                                    <field name="payment_method" widget="radio"/>
                                </group>
                            </group>
                            
                            <group string="Détails Chèque" attrs="{'invisible': [('payment_method', '!=', 'check')]}">
                                <group>
                                    <field name="check_number"/>
                                    <field name="check_date"/>
                                </group>
                                <group>
                                    <field name="bank_id" options="{'no_create': True}"/>
                                </group>
                            </group>
                            
                            <group string="Détails Virement" attrs="{'invisible': [('payment_method', '!=', 'transfer')]}">
                                <group>
                                    <field name="transfer_reference"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Vérification Documents">
                            <group>
                                <group>
                                    <field name="invoice_verified" widget="boolean_toggle"/>
                                    <field name="bordereau_verified" widget="boolean_toggle"/>
                                    <field name="mainlevee_verified" widget="boolean_toggle"/>
                                </group>
                                <group>
                                    <field name="all_documents_verified" readonly="1" widget="boolean_toggle"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Validation">
                            <group>
                                <group>
                                    <field name="validated_by" readonly="1"/>
                                    <field name="validation_date" readonly="1"/>
                                </group>
                                <group>
                                    <field name="rejection_reason" readonly="1" attrs="{'invisible': [('state', '!=', 'rejected')]}"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Références">
                            <group>
                                <group>
                                    <field name="receipt_id" readonly="1"/>
                                    <field name="receipt_number" readonly="1"/>
                                </group>
                                <group>
                                    <field name="lettrage_id" readonly="1"/>
                                    <field name="port_fee_id" readonly="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Payment Search View -->
    <record id="view_tresorerie_payment_search" model="ir.ui.view">
        <field name="name">tresorerie.payment.search</field>
        <field name="model">tresorerie.payment</field>
        <field name="arch" type="xml">
            <search string="Rechercher Paiements">
                <field name="name" string="Numéro"/>
                <field name="partner_name" string="Redevable"/>
                <field name="bordereau_id" string="Bordereau"/>
                <field name="invoice_id" string="Facture"/>
                <field name="cashier_id" string="Caissier"/>
                <field name="caisse_id" string="Caisse"/>
                
                <filter name="draft" string="Brouillon" domain="[('state', '=', 'draft')]"/>
                <filter name="pending" string="En attente" domain="[('state', '=', 'pending')]"/>
                <filter name="validated" string="Validé" domain="[('state', '=', 'validated')]"/>
                <filter name="rejected" string="Rejeté" domain="[('state', '=', 'rejected')]"/>
                
                <separator/>
                <filter name="my_payments" string="Mes Paiements" domain="[('cashier_id', '=', uid)]"/>
                <filter name="partial_payments" string="Paiements Partiels" domain="[('is_partial_payment', '=', True)]"/>
                
                <separator/>
                <filter name="today" string="Aujourd'hui" domain="[('date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('date', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <filter name="this_week" string="Cette semaine" domain="[('date', '&gt;=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                <filter name="this_month" string="Ce mois" domain="[('date', '&gt;=', context_today().strftime('%Y-%m-01'))]"/>
                
                <group expand="0" string="Grouper par">
                    <filter name="group_state" string="Statut" context="{'group_by': 'state'}"/>
                    <filter name="group_cashier" string="Caissier" context="{'group_by': 'cashier_id'}"/>
                    <filter name="group_caisse" string="Caisse" context="{'group_by': 'caisse_id'}"/>
                    <filter name="group_payment_method" string="Mode de paiement" context="{'group_by': 'payment_method'}"/>
                    <filter name="group_date" string="Date" context="{'group_by': 'date'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Payment Kanban View -->
    <record id="view_tresorerie_payment_kanban" model="ir.ui.view">
        <field name="name">tresorerie.payment.kanban</field>
        <field name="model">tresorerie.payment</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" class="o_kanban_small_column">
                <field name="name"/>
                <field name="partner_name"/>
                <field name="amount_paid"/>
                <field name="payment_method"/>
                <field name="date"/>
                <field name="state"/>
                <field name="currency_id"/>
                <field name="cashier_id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div><strong><field name="partner_name"/></strong></div>
                                    <div><field name="payment_method"/></div>
                                    <div class="text-muted">
                                        <field name="cashier_id"/>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_record_bottom">
                                <div class="oe_kanban_bottom_left">
                                    <field name="amount_paid" widget="monetary"/>
                                </div>
                                <div class="oe_kanban_bottom_right">
                                    <field name="date"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_tresorerie_payment" model="ir.actions.act_window">
        <field name="name">Paiements Trésorerie</field>
        <field name="res_model">tresorerie.payment</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="context">{'search_default_this_month': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Enregistrer votre premier paiement
            </p>
            <p>
                Les paiements sont enregistrés après vérification des documents
                et doivent être validés par le chef de trésorerie.
            </p>
        </field>
    </record>

    <!-- Action for pending validation -->
    <record id="action_payment_pending_validation" model="ir.actions.act_window">
        <field name="name">Paiements en Attente de Validation</field>
        <field name="res_model">tresorerie.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'pending')]</field>
    </record>

    <!-- Action for my payments -->
    <record id="action_my_payments" model="ir.actions.act_window">
        <field name="name">Mes Paiements</field>
        <field name="res_model">tresorerie.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_my_payments': 1, 'search_default_this_month': 1}</field>
    </record>
</odoo>
