from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

class BankOperation(models.Model):
    _name = 'caisse.bank.operation'
    _description = 'Opération Bancaire'
    _order = 'date desc'

    name = fields.Char('Référence', required=True, default='Nouveau')
    operation_type = fields.Selection([
        ('deposit', 'Dépôt'),
        ('withdrawal', 'Retrait'),
        ('transfer', 'Virement')
    ], 'Type d\'opération', required=True)
    
    company_id = fields.Many2one('res.company', 'Société', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', 'Devise', related='company_id.currency_id')
    
    amount = fields.Monetary('Montant', required=True, currency_field='currency_id')
    date = fields.Date('Date', required=True, default=fields.Date.today)
    description = fields.Text('Description')
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('confirmed', 'Confirmé'),
        ('done', 'Effectué')
    ], default='draft')
    
    caisse_id = fields.Many2one('caisse.caisse', 'Caisse')
    movement_id = fields.Many2one('caisse.movement', 'Mouvement associé')

    @api.model
    def create(self, vals):
        if vals.get('name', 'Nouveau') == 'Nouveau':
            sequence = self.env['ir.sequence'].next_by_code('caisse.bank.operation')
            vals['name'] = sequence or 'BOP'
        return super().create(vals)

