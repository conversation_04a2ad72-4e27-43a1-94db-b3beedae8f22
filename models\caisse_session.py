from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

class CaisseSession(models.Model):
    _name = 'caisse.session'
    _description = 'Session de Caisse'
    _order = 'date_start desc'

    name = fields.Char('Référence', required=True, default='Nouvelle Session')
    caisse_id = fields.Many2one('caisse.caisse', 'Caisse', required=True)
    user_id = fields.Many2one('res.users', 'Utilisateur', required=True, default=lambda self: self.env.user)
    
    date_start = fields.Datetime('Date d\'ouverture')
    date_stop = fields.Datetime('Date de fermeture')
    
    company_id = fields.Many2one('res.company', 'Société', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', 'Devise', related='company_id.currency_id')
    
    opening_balance = fields.Monetary('Solde d\'ouverture', currency_field='currency_id')
    closing_balance = fields.Monetary('Solde de fermeture', currency_field='currency_id')
    difference = fields.Monetary('Différence', compute='_compute_difference', currency_field='currency_id')
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('open', 'Ouverte'),
        ('closed', 'Fermée')
    ], default='draft')
    
    movement_ids = fields.One2many('caisse.movement', 'session_id', 'Mouvements')
    movement_count = fields.Integer('Nombre de mouvements', compute='_compute_movement_count')

    @api.depends('opening_balance', 'closing_balance')
    def _compute_difference(self):
        for session in self:
            session.difference = session.closing_balance - session.opening_balance
    
    @api.depends('movement_ids')
    def _compute_movement_count(self):
        for session in self:
            session.movement_count = len(session.movement_ids)

    @api.model
    def create(self, vals):
        if vals.get('name', 'Nouvelle Session') == 'Nouvelle Session':
            sequence = self.env['ir.sequence'].next_by_code('caisse.session')
            vals['name'] = sequence or 'Session'
        return super().create(vals)

    def open_session(self):
        self.ensure_one()
        if self.state != 'draft':
            raise UserError(_('Seules les sessions en brouillon peuvent être ouvertes.'))
        
        self.write({
            'state': 'open',
            'date_start': fields.Datetime.now(),
        })

    def close_session(self):
        self.ensure_one()
        if self.state != 'open':
            raise UserError(_('Seules les sessions ouvertes peuvent être fermées.'))
        
        self.write({
            'state': 'closed',
            'date_stop': fields.Datetime.now(),
            'closing_balance': self.caisse_id.balance,
        })

    def action_view_movements(self):
        return {
            'type': 'ir.actions.act_window',
            'name': f'Mouvements - {self.name}',
            'res_model': 'caisse.movement',
            'view_mode': 'tree,form',
            'domain': [('session_id', '=', self.id)],
            'context': {'default_session_id': self.id, 'default_caisse_id': self.caisse_id.id},
        }

