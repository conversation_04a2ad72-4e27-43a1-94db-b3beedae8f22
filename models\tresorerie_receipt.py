from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

class TresorerieReceipt(models.Model):
    _name = 'tresorerie.receipt'
    _description = 'Reçu de Paiement'
    _order = 'date desc, name desc'
    _rec_name = 'name'

    name = fields.Char('Numéro de Reçu', required=True, default='Nouveau', readonly=True)
    date = fields.Datetime('Date d\'émission', required=True, default=fields.Datetime.now)
    
    # Références
    payment_id = fields.Many2one('tresorerie.payment', 'Paiement', required=True, ondelete='cascade')
    bordereau_id = fields.Many2one('tresorerie.bordereau', 'Bordereau', related='payment_id.bordereau_id', store=True)
    invoice_id = fields.Many2one('account.move', 'Facture', related='payment_id.invoice_id', store=True)
    
    # Informations du redevable
    partner_id = fields.Many2one('res.partner', 'Redevable', required=True)
    partner_name = fields.Char('Nom du redevable', related='partner_id.name', store=True)
    partner_address = fields.Text('Adresse', compute='_compute_partner_address', store=True)
    
    # Montants
    company_id = fields.Many2one('res.company', 'Société', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', 'Devise', related='company_id.currency_id')
    amount = fields.Monetary('Montant payé', required=True, currency_field='currency_id')
    
    # Mode de paiement
    payment_method = fields.Selection([
        ('cash', 'Espèces'),
        ('check', 'Chèque'),
        ('transfer', 'Virement bancaire'),
        ('card', 'Carte bancaire')
    ], string='Mode de paiement', required=True)
    
    payment_method_display = fields.Char('Mode de paiement (affichage)', compute='_compute_payment_method_display', store=True)
    
    # Détails du paiement
    check_number = fields.Char('Numéro de chèque', related='payment_id.check_number', store=True)
    check_date = fields.Date('Date du chèque', related='payment_id.check_date', store=True)
    bank_name = fields.Char('Banque émettrice', related='payment_id.bank_id.name', store=True)
    transfer_reference = fields.Char('Référence de virement', related='payment_id.transfer_reference', store=True)
    
    # Informations de la redevance
    vessel_name = fields.Char('Nom du navire', related='payment_id.port_fee_id.vessel_name', store=True)
    vessel_type = fields.Selection(related='payment_id.port_fee_id.vessel_type', store=True)
    tonnage = fields.Float('Tonnage', related='payment_id.port_fee_id.tonnage', store=True)
    
    # Caisse et utilisateur
    caisse_id = fields.Many2one('caisse.caisse', 'Caisse', related='payment_id.caisse_id', store=True)
    cashier_id = fields.Many2one('res.users', 'Caissier', related='payment_id.cashier_id', store=True)
    cashier_name = fields.Char('Nom du caissier', related='cashier_id.name', store=True)
    
    # Statut
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('issued', 'Émis'),
        ('printed', 'Imprimé'),
        ('cancelled', 'Annulé')
    ], default='draft', string='Statut', tracking=True)
    
    # Impression
    print_count = fields.Integer('Nombre d\'impressions', default=0)
    last_print_date = fields.Datetime('Dernière impression')
    last_print_user = fields.Many2one('res.users', 'Dernière impression par')
    
    # Archivage
    archived = fields.Boolean('Archivé', default=False)
    archive_date = fields.Datetime('Date d\'archivage')
    archive_location = fields.Char('Emplacement d\'archivage')
    
    @api.depends('payment_method', 'check_number', 'transfer_reference')
    def _compute_payment_method_display(self):
        for receipt in self:
            if receipt.payment_method == 'cash':
                receipt.payment_method_display = 'Espèces'
            elif receipt.payment_method == 'check':
                display = 'Chèque'
                if receipt.check_number:
                    display += f' n° {receipt.check_number}'
                if receipt.bank_name:
                    display += f' ({receipt.bank_name})'
                receipt.payment_method_display = display
            elif receipt.payment_method == 'transfer':
                display = 'Virement bancaire'
                if receipt.transfer_reference:
                    display += f' - Réf: {receipt.transfer_reference}'
                receipt.payment_method_display = display
            elif receipt.payment_method == 'card':
                receipt.payment_method_display = 'Carte bancaire'
            else:
                receipt.payment_method_display = receipt.payment_method or ''

    @api.depends('partner_id')
    def _compute_partner_address(self):
        """Compute partner address"""
        for record in self:
            if record.partner_id:
                address_parts = []
                if record.partner_id.street:
                    address_parts.append(record.partner_id.street)
                if record.partner_id.street2:
                    address_parts.append(record.partner_id.street2)
                if record.partner_id.city:
                    address_parts.append(record.partner_id.city)
                if record.partner_id.zip:
                    address_parts.append(record.partner_id.zip)
                if record.partner_id.country_id:
                    address_parts.append(record.partner_id.country_id.name)
                record.partner_address = '\n'.join(address_parts)
            else:
                record.partner_address = ''
    
    @api.model
    def create(self, vals):
        if vals.get('name', 'Nouveau') == 'Nouveau':
            sequence = self.env['ir.sequence'].next_by_code('tresorerie.receipt')
            vals['name'] = sequence or 'REC'
        
        # Automatiquement émettre le reçu à la création
        vals['state'] = 'issued'
        
        return super().create(vals)
    
    def action_print_receipt(self):
        """Imprimer le reçu"""
        self.ensure_one()
        
        if self.state == 'cancelled':
            raise UserError(_('Impossible d\'imprimer un reçu annulé.'))
        
        # Mettre à jour les informations d'impression
        self.print_count += 1
        self.last_print_date = fields.Datetime.now()
        self.last_print_user = self.env.user
        
        if self.state == 'issued':
            self.state = 'printed'
        
        # Retourner l'action d'impression du rapport
        return self.env.ref('tresorie.action_report_receipt').report_action(self)
    
    def action_cancel_receipt(self):
        """Annuler le reçu"""
        self.ensure_one()
        
        # Vérifier les permissions
        if not self.env.user.has_group('tresorie.group_treasury_chief'):
            raise UserError(_('Seul le chef de trésorerie peut annuler un reçu.'))
        
        if self.state == 'cancelled':
            raise UserError(_('Le reçu est déjà annulé.'))
        
        self.state = 'cancelled'
        
        self.message_post(
            body=_("Reçu annulé par %s") % self.env.user.name,
            message_type='notification'
        )
    
    def action_duplicate_receipt(self):
        """Créer un duplicata du reçu"""
        self.ensure_one()
        
        if self.state == 'cancelled':
            raise UserError(_('Impossible de dupliquer un reçu annulé.'))
        
        # Créer une copie avec un nouveau numéro
        duplicate = self.copy({
            'name': 'Nouveau',  # Sera remplacé par la séquence
            'date': fields.Datetime.now(),
            'print_count': 0,
            'last_print_date': False,
            'last_print_user': False,
            'state': 'issued'
        })
        
        # Ajouter une mention "DUPLICATA" dans le nom
        duplicate.name = duplicate.name + ' (DUPLICATA)'
        
        self.message_post(
            body=_("Duplicata créé: %s") % duplicate.name,
            message_type='notification'
        )
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Duplicata de reçu'),
            'res_model': 'tresorerie.receipt',
            'res_id': duplicate.id,
            'view_mode': 'form',
            'target': 'current',
        }
    
    def action_archive_receipt(self):
        """Archiver le reçu"""
        self.ensure_one()
        
        if self.archived:
            raise UserError(_('Le reçu est déjà archivé.'))
        
        self.archived = True
        self.archive_date = fields.Datetime.now()
        
        # Générer automatiquement un emplacement d'archivage
        year = fields.Date.today().year
        month = fields.Date.today().month
        self.archive_location = f"ARCH-{year}-{month:02d}"
        
        self.message_post(
            body=_("Reçu archivé par %s - Emplacement: %s") % (self.env.user.name, self.archive_location),
            message_type='notification'
        )
    
    def name_get(self):
        result = []
        for record in self:
            name = f"{record.name}"
            if record.state == 'cancelled':
                name += " (ANNULÉ)"
            elif record.archived:
                name += " (ARCHIVÉ)"
            result.append((record.id, name))
        return result
    
    @api.constrains('amount')
    def _check_amount(self):
        for receipt in self:
            if receipt.amount <= 0:
                raise ValidationError(_('Le montant du reçu doit être positif.'))
