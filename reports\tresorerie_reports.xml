<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Receipt Report -->
    <record id="action_report_receipt" model="ir.actions.report">
        <field name="name">Re<PERSON><PERSON> de <PERSON>iement</field>
        <field name="model">tresorerie.receipt</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">tresorie.report_receipt_template</field>
        <field name="report_file">tresorie.report_receipt_template</field>
        <field name="binding_model_id" ref="model_tresorerie_receipt"/>
        <field name="binding_type">report</field>
    </record>

    <template id="report_receipt_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="receipt">
                <t t-call="web.external_layout">
                    <div class="page">
                        <div class="oe_structure"/>
                        
                        <div class="row">
                            <div class="col-12">
                                <h2 class="text-center">REÇU DE PAIEMENT</h2>
                                <h3 class="text-center"><t t-esc="receipt.name"/></h3>
                            </div>
                        </div>
                        
                        <div class="row mt32">
                            <div class="col-6">
                                <strong>Date:</strong> <t t-esc="receipt.date"/>
                            </div>
                            <div class="col-6">
                                <strong>Caisse:</strong> <t t-esc="receipt.caisse_id.name"/>
                            </div>
                        </div>
                        
                        <div class="row mt16">
                            <div class="col-12">
                                <strong>Redevable:</strong> <t t-esc="receipt.partner_name"/>
                            </div>
                        </div>
                        
                        <div class="row mt16">
                            <div class="col-12">
                                <strong>Adresse:</strong><br/>
                                <t t-esc="receipt.partner_address"/>
                            </div>
                        </div>
                        
                        <div class="row mt32">
                            <div class="col-6">
                                <strong>Navire:</strong> <t t-esc="receipt.vessel_name"/>
                            </div>
                            <div class="col-6">
                                <strong>Type:</strong> <t t-esc="receipt.vessel_type"/>
                            </div>
                        </div>
                        
                        <div class="row mt16">
                            <div class="col-6">
                                <strong>Tonnage:</strong> <t t-esc="receipt.tonnage"/>
                            </div>
                            <div class="col-6">
                                <strong>Mode de paiement:</strong> <t t-esc="receipt.payment_method_display"/>
                            </div>
                        </div>
                        
                        <t t-if="receipt.payment_method == 'check'">
                            <div class="row mt16">
                                <div class="col-6">
                                    <strong>N° Chèque:</strong> <t t-esc="receipt.check_number"/>
                                </div>
                                <div class="col-6">
                                    <strong>Date Chèque:</strong> <t t-esc="receipt.check_date"/>
                                </div>
                            </div>
                            <div class="row mt8">
                                <div class="col-12">
                                    <strong>Banque:</strong> <t t-esc="receipt.bank_name"/>
                                </div>
                            </div>
                        </t>
                        
                        <t t-if="receipt.payment_method == 'transfer'">
                            <div class="row mt16">
                                <div class="col-12">
                                    <strong>Référence Virement:</strong> <t t-esc="receipt.transfer_reference"/>
                                </div>
                            </div>
                        </t>
                        
                        <div class="row mt32">
                            <div class="col-12 text-center">
                                <h3>MONTANT PAYÉ: <t t-esc="receipt.amount" t-options="{'widget': 'monetary', 'display_currency': receipt.currency_id}"/></h3>
                            </div>
                        </div>
                        
                        <div class="row mt32">
                            <div class="col-6">
                                <strong>Caissier:</strong> <t t-esc="receipt.cashier_name"/>
                            </div>
                            <div class="col-6 text-right">
                                <strong>Signature:</strong>
                                <div style="border-bottom: 1px solid black; width: 150px; height: 50px; display: inline-block; margin-left: 10px;"></div>
                            </div>
                        </div>
                        
                        <div class="row mt16">
                            <div class="col-12">
                                <small>
                                    <t t-if="receipt.print_count > 1">
                                        DUPLICATA N°<t t-esc="receipt.print_count - 1"/>
                                    </t>
                                </small>
                            </div>
                        </div>
                        
                        <div class="oe_structure"/>
                    </div>
                </t>
            </t>
        </t>
    </template>

    <!-- Bordereau Report -->
    <record id="action_report_bordereau" model="ir.actions.report">
        <field name="name">Bordereau de Versement</field>
        <field name="model">tresorerie.bordereau</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">tresorie.report_bordereau_template</field>
        <field name="report_file">tresorie.report_bordereau_template</field>
        <field name="binding_model_id" ref="model_tresorerie_bordereau"/>
        <field name="binding_type">report</field>
    </record>

    <template id="report_bordereau_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="bordereau">
                <t t-call="web.external_layout">
                    <div class="page">
                        <div class="oe_structure"/>
                        
                        <div class="row">
                            <div class="col-12">
                                <h2 class="text-center">BORDEREAU DE VERSEMENT</h2>
                                <h3 class="text-center"><t t-esc="bordereau.name"/></h3>
                            </div>
                        </div>
                        
                        <div class="row mt32">
                            <div class="col-6">
                                <strong>Date:</strong> <t t-esc="bordereau.date"/>
                            </div>
                            <div class="col-6">
                                <strong>Receveur:</strong> <t t-esc="bordereau.receiver_id.name"/>
                            </div>
                        </div>
                        
                        <div class="row mt16">
                            <div class="col-12">
                                <strong>Redevable:</strong> <t t-esc="bordereau.partner_name"/>
                            </div>
                        </div>
                        
                        <div class="row mt8">
                            <div class="col-12">
                                <strong>NUI:</strong> <t t-esc="bordereau.partner_nui"/>
                            </div>
                        </div>
                        
                        <div class="row mt16">
                            <div class="col-12">
                                <strong>Adresse:</strong><br/>
                                <t t-esc="bordereau.partner_address"/>
                            </div>
                        </div>
                        
                        <div class="row mt32">
                            <div class="col-6">
                                <strong>Navire:</strong> <t t-esc="bordereau.vessel_name"/>
                            </div>
                            <div class="col-6">
                                <strong>Type:</strong> <t t-esc="bordereau.vessel_type"/>
                            </div>
                        </div>
                        
                        <div class="row mt16">
                            <div class="col-6">
                                <strong>Tonnage:</strong> <t t-esc="bordereau.tonnage"/>
                            </div>
                            <div class="col-6">
                                <strong>Mainlevée:</strong> <t t-esc="bordereau.mainlevee_reference"/>
                            </div>
                        </div>
                        
                        <div class="row mt32">
                            <div class="col-12">
                                <strong>Description:</strong><br/>
                                <t t-esc="bordereau.description"/>
                            </div>
                        </div>
                        
                        <div class="row mt32">
                            <div class="col-12 text-center">
                                <h3>MONTANT À VERSER: <t t-esc="bordereau.amount" t-options="{'widget': 'monetary', 'display_currency': bordereau.currency_id}"/></h3>
                            </div>
                        </div>
                        
                        <div class="row mt64">
                            <div class="col-6">
                                <strong>Créé par:</strong> <t t-esc="bordereau.created_by.name"/><br/>
                                <strong>Date:</strong> <t t-esc="bordereau.created_date"/>
                            </div>
                            <div class="col-6 text-right">
                                <strong>Signature du Receveur:</strong><br/>
                                <div style="border: 1px solid black; width: 200px; height: 80px; display: inline-block; margin-top: 10px;"></div>
                                <br/>
                                <t t-if="bordereau.signature_date">
                                    Signé le: <t t-esc="bordereau.signature_date"/>
                                </t>
                            </div>
                        </div>
                        
                        <div class="oe_structure"/>
                    </div>
                </t>
            </t>
        </t>
    </template>

    <!-- Daily Collection Report -->
    <record id="action_report_daily_collection" model="ir.actions.report">
        <field name="name">Rapport Journalier d'Encaissement</field>
        <field name="model">tresorerie.payment</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">tresorie.report_daily_collection_template</field>
        <field name="report_file">tresorie.report_daily_collection_template</field>
    </record>

    <template id="report_daily_collection_template">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <div class="page">
                    <div class="oe_structure"/>
                    
                    <div class="row">
                        <div class="col-12">
                            <h2 class="text-center">RAPPORT JOURNALIER D'ENCAISSEMENT</h2>
                            <h4 class="text-center">Date: <t t-esc="context_today().strftime('%d/%m/%Y')"/></h4>
                        </div>
                    </div>
                    
                    <div class="row mt32">
                        <div class="col-12">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>N° Paiement</th>
                                        <th>Redevable</th>
                                        <th>Navire</th>
                                        <th>Montant</th>
                                        <th>Mode</th>
                                        <th>Caissier</th>
                                        <th>Caisse</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-set="total_amount" t-value="0"/>
                                    <t t-foreach="docs" t-as="payment">
                                        <tr>
                                            <td><t t-esc="payment.name"/></td>
                                            <td><t t-esc="payment.partner_name"/></td>
                                            <td><t t-esc="payment.bordereau_id.vessel_name"/></td>
                                            <td class="text-right">
                                                <t t-esc="payment.amount_paid" t-options="{'widget': 'monetary', 'display_currency': payment.currency_id}"/>
                                            </td>
                                            <td><t t-esc="dict(payment._fields['payment_method'].selection)[payment.payment_method]"/></td>
                                            <td><t t-esc="payment.cashier_id.name"/></td>
                                            <td><t t-esc="payment.caisse_id.name"/></td>
                                        </tr>
                                        <t t-set="total_amount" t-value="total_amount + payment.amount_paid"/>
                                    </t>
                                </tbody>
                                <tfoot>
                                    <tr class="table-active">
                                        <th colspan="3">TOTAL</th>
                                        <th class="text-right">
                                            <t t-esc="total_amount" t-options="{'widget': 'monetary'}"/>
                                        </th>
                                        <th colspan="3"></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    
                    <div class="oe_structure"/>
                </div>
            </t>
        </t>
    </template>

    <!-- Menu Items for Reports -->
    <menuitem id="menu_tresorerie_reports" name="Rapports" parent="caisse_menu_root" sequence="50"/>
    
    <menuitem id="menu_daily_collection_report" name="Encaissements du Jour" 
              parent="menu_tresorerie_reports" 
              action="action_daily_collection" 
              sequence="10"/>
              
    <menuitem id="menu_treasury_report" name="Rapport Trésorerie" 
              parent="menu_tresorerie_reports" 
              action="action_treasury_report" 
              sequence="20"/>
              
    <menuitem id="menu_collection_report" name="Rapport d'Encaissement" 
              parent="menu_tresorerie_reports" 
              action="action_collection_report" 
              sequence="30"/>
              
    <menuitem id="menu_refund_report" name="Rapport des Remboursements" 
              parent="menu_tresorerie_reports" 
              action="action_refund_report" 
              sequence="40"/>
</odoo>
