from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime

class CaisseCaisse(models.Model):
    _name = 'caisse.caisse'
    _description = 'Caisse'
    _order = 'name'

    name = fields.Char('Nom de la caisse', required=True)
    code = fields.Char('Code', required=True)
    company_id = fields.Many2one('res.company', 'Société', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', 'Devise', related='company_id.currency_id')
    journal_id = fields.Many2one('account.journal', 'Journal comptable', required=True, domain=[('type', '=', 'cash')])
    responsible_id = fields.Many2one('res.users', 'Responsable')
    active = fields.Boolean('Actif', default=True)
    
    # Soldes
    balance = fields.Monetary('Solde actuel', compute='_compute_balance', store=True)
    theoretical_balance = fields.Monetary('Solde théorique', compute='_compute_balance', store=True)
    
    # Sessions
    current_session_id = fields.Many2one('caisse.session', 'Session actuelle')
    session_ids = fields.One2many('caisse.session', 'caisse_id', 'Sessions')
    
    # Mouvements
    movement_ids = fields.One2many('caisse.movement', 'caisse_id', 'Mouvements')
    
    @api.depends('movement_ids.signed_amount', 'movement_ids.state')
    def _compute_balance(self):
        for caisse in self:
            movements = caisse.movement_ids.filtered(lambda m: m.state == 'done')
            balance = sum(movements.mapped('signed_amount'))
            caisse.balance = balance
            caisse.theoretical_balance = balance

    def action_view_movements(self):
        return {
            'type': 'ir.actions.act_window',
            'name': f'Mouvements - {self.name}',
            'res_model': 'caisse.movement',
            'view_mode': 'tree,form',
            'domain': [('caisse_id', '=', self.id)],
            'context': {'default_caisse_id': self.id},
        }

    def open_session(self):
        if self.current_session_id:
            raise UserError(_('Une session est déjà ouverte pour cette caisse.'))
        
        session = self.env['caisse.session'].create({
            'caisse_id': self.id,
            'user_id': self.env.user.id,
            'opening_balance': self.balance,
        })
        self.current_session_id = session.id
        return session.open_session()

    def close_current_session(self):
        if not self.current_session_id:
            raise UserError(_('Aucune session ouverte pour cette caisse.'))
        
        self.current_session_id.close_session()
        self.current_session_id = False

    @api.constrains('code')
    def _check_code_unique(self):
        for record in self:
            if self.search_count([('code', '=', record.code), ('id', '!=', record.id)]) > 0:
                raise ValidationError(_('Le code de la caisse doit être unique.'))
                
