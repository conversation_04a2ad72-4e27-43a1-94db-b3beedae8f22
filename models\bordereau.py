from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime

class Bordereau(models.Model):
    _name = 'tresorerie.bordereau'
    _description = 'Bordereau de Versement'
    _order = 'date desc, name desc'
    _rec_name = 'name'

    name = fields.Char('Numéro <PERSON>', required=True, default='Nouveau', readonly=True)
    date = fields.Date('Date', required=True, default=fields.Date.today)
    
    # Informations sur le redevable
    partner_id = fields.Many2one('res.partner', 'Redevable', required=True)
    partner_name = fields.Char('Nom du redevable', related='partner_id.name', store=True)
    partner_nui = fields.Char('NUI', related='partner_id.vat', store=True)
    partner_address = fields.Text('Adresse', compute='_compute_partner_address', store=True)
    
    # Références
    port_fee_id = fields.Many2one('caisse.port.fee', 'Redevance Portuaire', required=True)
    invoice_id = fields.Many2one('account.move', 'Facture', domain=[('move_type', '=', 'out_invoice')])
    mainlevee_reference = fields.Char('Référence de Mainlevée')
    
    # Montants
    company_id = fields.Many2one('res.company', 'Société', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', 'Devise', related='company_id.currency_id')
    amount = fields.Monetary('Montant à verser', required=True, currency_field='currency_id')
    
    # Description et détails
    description = fields.Text('Description')
    vessel_name = fields.Char('Nom du navire', related='port_fee_id.vessel_name', store=True)
    vessel_type = fields.Selection(related='port_fee_id.vessel_type', store=True)
    tonnage = fields.Float('Tonnage', related='port_fee_id.tonnage', store=True)
    
    # Statut et workflow
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('generated', 'Généré'),
        ('signed', 'Signé'),
        ('paid', 'Payé'),
        ('cancelled', 'Annulé')
    ], default='draft', string='Statut', tracking=True)
    
    # Signature et validation
    receiver_id = fields.Many2one('res.users', 'Receveur', default=lambda self: self.env.user)
    signature_date = fields.Datetime('Date de signature')
    signed_by = fields.Many2one('res.users', 'Signé par')
    
    # Paiement
    payment_id = fields.Many2one('tresorerie.payment', 'Paiement associé')
    payment_date = fields.Datetime('Date de paiement')
    
    # Audit trail
    created_by = fields.Many2one('res.users', 'Créé par', default=lambda self: self.env.user, readonly=True)
    created_date = fields.Datetime('Date de création', default=fields.Datetime.now, readonly=True)
    
    @api.model
    def create(self, vals):
        if vals.get('name', 'Nouveau') == 'Nouveau':
            sequence = self.env['ir.sequence'].next_by_code('tresorerie.bordereau')
            vals['name'] = sequence or 'BDV'
        return super().create(vals)

    @api.depends('partner_id')
    def _compute_partner_address(self):
        """Compute partner address"""
        for record in self:
            if record.partner_id:
                address_parts = []
                if record.partner_id.street:
                    address_parts.append(record.partner_id.street)
                if record.partner_id.street2:
                    address_parts.append(record.partner_id.street2)
                if record.partner_id.city:
                    address_parts.append(record.partner_id.city)
                if record.partner_id.zip:
                    address_parts.append(record.partner_id.zip)
                if record.partner_id.country_id:
                    address_parts.append(record.partner_id.country_id.name)
                record.partner_address = '\n'.join(address_parts)
            else:
                record.partner_address = ''

    @api.onchange('port_fee_id')
    def _onchange_port_fee_id(self):
        if self.port_fee_id:
            self.amount = self.port_fee_id.total_amount
            self.partner_id = self.port_fee_id.partner_id if hasattr(self.port_fee_id, 'partner_id') else False
            self.description = f"Redevance portuaire - {self.port_fee_id.vessel_name}"
    
    def action_generate(self):
        """Générer le bordereau"""
        self.ensure_one()
        if self.state != 'draft':
            raise UserError(_('Seuls les bordereaux en brouillon peuvent être générés.'))
        
        # Vérifications avant génération
        if not self.partner_id:
            raise UserError(_('Le redevable doit être spécifié.'))
        if not self.port_fee_id:
            raise UserError(_('La redevance portuaire doit être spécifiée.'))
        if self.amount <= 0:
            raise UserError(_('Le montant doit être positif.'))
        
        self.state = 'generated'
        
        # Log the generation
        self.message_post(
            body=_("Bordereau généré par %s") % self.env.user.name,
            message_type='notification'
        )
    
    def action_sign(self):
        """Signer le bordereau"""
        self.ensure_one()
        if self.state != 'generated':
            raise UserError(_('Seuls les bordereaux générés peuvent être signés.'))
        
        self.state = 'signed'
        self.signature_date = fields.Datetime.now()
        self.signed_by = self.env.user
        
        # Log the signature
        self.message_post(
            body=_("Bordereau signé par %s") % self.env.user.name,
            message_type='notification'
        )
    
    def action_cancel(self):
        """Annuler le bordereau"""
        self.ensure_one()
        if self.state == 'paid':
            raise UserError(_('Impossible d\'annuler un bordereau déjà payé.'))
        
        self.state = 'cancelled'
        
        # Log the cancellation
        self.message_post(
            body=_("Bordereau annulé par %s") % self.env.user.name,
            message_type='notification'
        )
    
    def action_reset_to_draft(self):
        """Remettre en brouillon"""
        self.ensure_one()
        if self.state == 'paid':
            raise UserError(_('Impossible de remettre en brouillon un bordereau payé.'))
        
        self.state = 'draft'
        self.signature_date = False
        self.signed_by = False
    
    @api.constrains('amount')
    def _check_amount(self):
        for record in self:
            if record.amount <= 0:
                raise ValidationError(_('Le montant doit être positif.'))
    
    def name_get(self):
        result = []
        for record in self:
            name = f"{record.name} - {record.partner_name or 'Sans redevable'}"
            result.append((record.id, name))
        return result
