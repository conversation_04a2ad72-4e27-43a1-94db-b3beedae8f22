<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Cash Flow Report -->
    <record id="action_cash_flow_report" model="ir.actions.act_window">
        <field name="name">Rapport de Flux de Trésorerie</field>
        <field name="res_model">caisse.movement</field>
        <field name="view_mode">pivot,graph</field>
        <field name="context">{
            'search_default_this_month': 1,
            'group_by': ['caisse_id', 'category', 'type']
        }</field>
    </record>

    <!-- Port Fee Revenue Report -->
    <record id="action_port_fee_report" model="ir.actions.act_window">
        <field name="name">Rapport Redevances Portuaires</field>
        <field name="res_model">caisse.port.fee</field>
        <field name="view_mode">pivot,graph</field>
        <field name="context">{
            'search_default_this_month': 1,
            'group_by': ['vessel_type', 'state']
        }</field>
    </record>

    <!-- Banking Operations Report -->
    <record id="action_banking_report" model="ir.actions.act_window">
        <field name="name">Rapport Opérations Bancaires</field>
        <field name="res_model">caisse.bank.operation</field>
        <field name="view_mode">pivot,graph</field>
        <field name="context">{
            'search_default_this_month': 1,
            'group_by': ['operation_type', 'state']
        }</field>
    </record>

    <!-- Revenue Collection Report -->
    <record id="action_revenue_report" model="ir.actions.act_window">
        <field name="name">Rapport Régies de Recettes</field>
        <field name="res_model">caisse.revenue.collection</field>
        <field name="view_mode">pivot,graph</field>
        <field name="context">{
            'search_default_this_month': 1,
            'group_by': ['collector_id', 'state']
        }</field>
    </record>
</odoo>