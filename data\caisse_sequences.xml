<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Port Fee Sequence -->
    <record id="seq_port_fee" model="ir.sequence">
        <field name="name">Redevance Portuaire</field>
        <field name="code">caisse.port.fee</field>
        <field name="prefix">REP/%(year)s/</field>
        <field name="padding">5</field>
        <field name="company_id" eval="False"/>
    </record>
    
    <!-- Bank Operation Sequence -->
    <record id="seq_bank_operation" model="ir.sequence">
        <field name="name">Opération Bancaire</field>
        <field name="code">caisse.bank.operation</field>
        <field name="prefix">BOP/%(year)s/</field>
        <field name="padding">5</field>
        <field name="company_id" eval="False"/>
    </record>
    
    <!-- Deposit Slip Sequence -->
    <record id="seq_deposit_slip" model="ir.sequence">
        <field name="name">Bordereau de Versement</field>
        <field name="code">caisse.deposit.slip</field>
        <field name="prefix">BDV/%(year)s/</field>
        <field name="padding">5</field>
        <field name="company_id" eval="False"/>
    </record>
    
    <!-- Revenue Collection Sequence -->
    <record id="seq_revenue_collection" model="ir.sequence">
        <field name="name">Régie de Recettes</field>
        <field name="code">caisse.revenue.collection</field>
        <field name="prefix">REC/%(year)s/</field>
        <field name="padding">5</field>
        <field name="company_id" eval="False"/>
    </record>

    <!-- Treasury Sequences -->

    <!-- Bordereau Sequence -->
    <record id="seq_tresorerie_bordereau" model="ir.sequence">
        <field name="name">Bordereau de Versement Trésorerie</field>
        <field name="code">tresorerie.bordereau</field>
        <field name="prefix">BDV/%(year)s/</field>
        <field name="padding">6</field>
        <field name="company_id" eval="False"/>
    </record>

    <!-- Payment Sequence -->
    <record id="seq_tresorerie_payment" model="ir.sequence">
        <field name="name">Paiement Trésorerie</field>
        <field name="code">tresorerie.payment</field>
        <field name="prefix">PAY/%(year)s/</field>
        <field name="padding">6</field>
        <field name="company_id" eval="False"/>
    </record>

    <!-- Receipt Sequence -->
    <record id="seq_tresorerie_receipt" model="ir.sequence">
        <field name="name">Reçu de Paiement</field>
        <field name="code">tresorerie.receipt</field>
        <field name="prefix">REC/%(year)s/</field>
        <field name="padding">6</field>
        <field name="company_id" eval="False"/>
    </record>

    <!-- Lettrage Sequence -->
    <record id="seq_tresorerie_lettrage" model="ir.sequence">
        <field name="name">Lettrage Trésorerie</field>
        <field name="code">tresorerie.lettrage</field>
        <field name="prefix">LET/%(year)s/</field>
        <field name="padding">6</field>
        <field name="company_id" eval="False"/>
    </record>

    <!-- Refund Sequence -->
    <record id="seq_tresorerie_refund" model="ir.sequence">
        <field name="name">Remboursement Trésorerie</field>
        <field name="code">tresorerie.refund</field>
        <field name="prefix">RMB/%(year)s/</field>
        <field name="padding">6</field>
        <field name="company_id" eval="False"/>
    </record>
</odoo>