from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

class CaisseMovement(models.Model):
    _name = 'caisse.movement'
    _description = 'Mouvement de Caisse'
    _order = 'date desc'

    name = fields.Char('Référence', required=True, default='Nouveau')
    caisse_id = fields.Many2one('caisse.caisse', 'Caisse', required=True)
    session_id = fields.Many2one('caisse.session', 'Session')
    type = fields.Selection([
        ('in', 'Entrée'),
        ('out', 'Sortie')
    ], 'Type', required=True)
    
    category = fields.Selection([
        ('opening', 'Ouverture'),
        ('closing', 'Fermeture'),
        ('sale', 'Vente'),
        ('expense', 'Dépense'),
        ('transfer', 'Virement'),
        ('bank', 'Opération bancaire'),
        ('port_fee', 'Redevance portuaire'),
        ('other', 'Autre')
    ], 'Catégorie', required=True)
    
    amount = fields.Monetary('Montant', required=True, currency_field='currency_id')
    signed_amount = fields.Monetary('Montant signé', compute='_compute_signed_amount', store=True)
    description = fields.Text('Description')
    date = fields.Datetime('Date', required=True, default=fields.Datetime.now)
    
    company_id = fields.Many2one('res.company', 'Société', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', 'Devise', related='company_id.currency_id')
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('done', 'Validé'),
        ('cancelled', 'Annulé')
    ], default='draft')
    
    validator_id = fields.Many2one('res.users', 'Validé par')
    validation_date = fields.Datetime('Date de validation')
    
    move_id = fields.Many2one('account.move', 'Écriture comptable', readonly=True)
    transfer_id = fields.Many2one('caisse.transfer', 'Virement lié')

    @api.depends('amount', 'type')
    def _compute_signed_amount(self):
        for movement in self:
            if movement.type == 'in':
                movement.signed_amount = movement.amount
            else:
                movement.signed_amount = -movement.amount

    @api.model
    def create(self, vals):
        if vals.get('name', 'Nouveau') == 'Nouveau':
            sequence = self.env['ir.sequence'].next_by_code('caisse.movement')
            vals['name'] = sequence or 'MOV'
        return super().create(vals)

    @api.constrains('amount')
    def _check_amount(self):
        for movement in self:
            if movement.amount <= 0:
                raise ValidationError(_('Le montant doit être positif.'))

    def validate_movement(self):
        self.ensure_one()
        if self.state != 'draft':
            raise UserError(_('Seuls les mouvements en brouillon peuvent être validés.'))
        
        # Check validation rights
        if not self.env.user.has_group('tresorie.group_caisse_validator'):
            raise UserError(_('Vous n\'avez pas les droits pour valider ce mouvement.'))
        
        self._create_account_move()
        
        self.validator_id = self.env.user.id
        self.validation_date = fields.Datetime.now()
        self.state = 'done'

    def cancel_movement(self):
        self.ensure_one()
        if self.state == 'done':
            raise UserError(_('Les mouvements validés ne peuvent pas être annulés.'))
        self.state = 'cancelled'

    def _create_account_move(self):
        if not self.caisse_id.journal_id:
            raise UserError(_('Aucun journal configuré pour cette caisse.'))
        
        move_vals = {
            'journal_id': self.caisse_id.journal_id.id,
            'date': self.date.date(),
            'ref': self.name,
            'line_ids': [
                (0, 0, {
                    'name': self.description or self.name,
                    'account_id': self.caisse_id.journal_id.default_account_id.id,
                    'debit': self.amount if self.type == 'in' else 0,
                    'credit': self.amount if self.type == 'out' else 0,
                }),
                (0, 0, {
                    'name': self.description or self.name,
                    'account_id': self._get_counterpart_account().id,
                    'debit': self.amount if self.type == 'out' else 0,
                    'credit': self.amount if self.type == 'in' else 0,
                }),
            ],
        }
        
        move = self.env['account.move'].create(move_vals)
        move.action_post()
        self.move_id = move.id

    def _get_counterpart_account(self):
        # Simple logic - should be enhanced based on category
        if self.category == 'sale':
            return self.env.company.account_sale_tax_id.account_id
        elif self.category == 'expense':
            return self.env.company.account_purchase_tax_id.account_id
        else:
            # Default to a generic account
            return self.env['account.account'].search([
                ('company_id', '=', self.company_id.id),
                ('user_type_id.type', '=', 'other')
            ], limit=1)









