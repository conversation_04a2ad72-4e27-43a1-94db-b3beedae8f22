<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Port Fee Tree View -->
    <record id="view_port_fee_tree" model="ir.ui.view">
        <field name="name">caisse.port.fee.tree</field>
        <field name="model">caisse.port.fee</field>
        <field name="arch" type="xml">
            <tree string="Redevances Portuaires">
                <field name="name"/>
                <field name="vessel_name"/>
                <field name="vessel_type"/>
                <field name="tonnage"/>
                <field name="total_amount"/>
                <field name="date"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Port Fee Form View -->
    <record id="view_port_fee_form" model="ir.ui.view">
        <field name="name">caisse.port.fee.form</field>
        <field name="model">caisse.port.fee</field>
        <field name="arch" type="xml">
            <form string="Redevance Portuaire">
                <header>
                    <button name="confirm_fee" type="object" string="Confirmer" 
                            class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="register_payment" type="object" string="Enregistrer Paiement" 
                            class="btn-primary" attrs="{'invisible': [('state', '!=', 'confirmed')]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,confirmed,paid"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="vessel_name"/>
                            <field name="vessel_type"/>
                            <field name="date"/>
                        </group>
                        <group>
                            <field name="tonnage"/>
                            <field name="fee_rate"/>
                            <field name="total_amount"/>
                            <field name="caisse_id" attrs="{'required': [('state', '=', 'confirmed')]}"/>
                        </group>
                    </group>
                    <group attrs="{'invisible': [('state', '!=', 'paid')]}">
                        <field name="payment_date"/>
                        <field name="movement_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Port Fee Action -->
    <record id="action_port_fee" model="ir.actions.act_window">
        <field name="name">Redevances Portuaires</field>
        <field name="res_model">caisse.port.fee</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Créer votre première redevance portuaire
            </p>
            <p>
                Gérez les redevances portuaires pour les navires.
            </p>
        </field>
    </record>
</odoo>

