<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Movement Form View -->
    <record id="view_caisse_movement_form" model="ir.ui.view">
        <field name="name">caisse.movement.form</field>
        <field name="model">caisse.movement</field>
        <field name="arch" type="xml">
            <form string="Mouvement de Caisse">
                <header>
                    <button name="validate_movement" type="object" string="Valider" 
                            class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="cancel_movement" type="object" string="Annuler" 
                            attrs="{'invisible': [('state', 'in', ['cancelled'])]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,done,cancelled"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="caisse_id"/>
                            <field name="session_id"/>
                            <field name="type"/>
                            <field name="category"/>
                        </group>
                        <group>
                            <field name="amount"/>
                            <field name="signed_amount"/>
                            <field name="date"/>
                            <field name="validator_id" attrs="{'invisible': [('state', '=', 'draft')]}"/>
                            <field name="validation_date" attrs="{'invisible': [('state', '=', 'draft')]}"/>
                        </group>
                    </group>
                    <group>
                        <field name="description"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Movement Tree View -->
    <record id="view_caisse_movement_tree" model="ir.ui.view">
        <field name="name">caisse.movement.tree</field>
        <field name="model">caisse.movement</field>
        <field name="arch" type="xml">
            <tree string="Mouvements de Caisse">
                <field name="name"/>
                <field name="caisse_id"/>
                <field name="type"/>
                <field name="category"/>
                <field name="amount"/>
                <field name="signed_amount"/>
                <field name="date"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Movement Action -->
    <record id="action_caisse_movement" model="ir.actions.act_window">
        <field name="name">Mouvements de Caisse</field>
        <field name="res_model">caisse.movement</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>


