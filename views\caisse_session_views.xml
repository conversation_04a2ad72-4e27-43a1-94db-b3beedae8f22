<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Session Form View -->
    <record id="view_caisse_session_form" model="ir.ui.view">
        <field name="name">caisse.session.form</field>
        <field name="model">caisse.session</field>
        <field name="arch" type="xml">
            <form string="Session de Caisse">
                <header>
                    <button name="open_session" type="object" string="Ouvrir Session" 
                            class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="close_session" type="object" string="Fermer Session" 
                            class="btn-primary" attrs="{'invisible': [('state', '!=', 'open')]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,open,closed"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button class="oe_stat_button" type="object" name="action_view_movements" icon="fa-list">
                            <field string="Mouvements" name="movement_count" widget="statinfo"/>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="caisse_id"/>
                            <field name="user_id"/>
                        </group>
                        <group>
                            <field name="date_start"/>
                            <field name="date_stop"/>
                            <field name="opening_balance"/>
                            <field name="closing_balance"/>
                            <field name="difference"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Session Tree View -->
    <record id="view_caisse_session_tree" model="ir.ui.view">
        <field name="name">caisse.session.tree</field>
        <field name="model">caisse.session</field>
        <field name="arch" type="xml">
            <tree string="Sessions de Caisse">
                <field name="name"/>
                <field name="caisse_id"/>
                <field name="user_id"/>
                <field name="date_start"/>
                <field name="date_stop"/>
                <field name="opening_balance"/>
                <field name="closing_balance"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Session Action -->
    <record id="action_caisse_session" model="ir.actions.act_window">
        <field name="name">Sessions de Caisse</field>
        <field name="res_model">caisse.session</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>


