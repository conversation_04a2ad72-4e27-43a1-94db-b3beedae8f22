id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_caisse_caisse_public,caisse.caisse.public,model_caisse_caisse,,1,1,1,1
access_caisse_session_public,caisse.session.public,model_caisse_session,,1,1,1,1
access_caisse_movement_public,caisse.movement.public,model_caisse_movement,,1,1,1,1
access_caisse_transfer_public,caisse.transfer.public,model_caisse_transfer,,1,1,1,1
access_port_fee_collector,caisse.port.fee.collector,model_caisse_port_fee,group_caisse_collector,1,1,1,0
access_port_fee_validator,caisse.port.fee.validator,model_caisse_port_fee,group_caisse_validator,1,1,1,1
access_bank_operation_collector,caisse.bank.operation.collector,model_caisse_bank_operation,group_caisse_collector,1,1,1,0
access_bank_operation_validator,caisse.bank.operation.validator,model_caisse_bank_operation,group_caisse_validator,1,1,1,1
access_deposit_slip_public,caisse.deposit.slip.public,model_caisse_deposit_slip,,1,1,1,1
access_revenue_collection_public,caisse.revenue.collection.public,model_caisse_revenue_collection,,1,1,1,1
access_revenue_category_public,caisse.revenue.category.public,model_caisse_revenue_category,,1,1,1,1

# Treasury Models Access Rights
# Bordereau Access
access_bordereau_cashier,tresorerie.bordereau.cashier,model_tresorerie_bordereau,group_treasury_cashier,1,1,1,0
access_bordereau_chief,tresorerie.bordereau.chief,model_tresorerie_bordereau,group_treasury_chief,1,1,1,1
access_bordereau_controller,tresorerie.bordereau.controller,model_tresorerie_bordereau,group_treasury_controller,1,0,0,0

# Treasury Payment Access
access_payment_cashier,tresorerie.payment.cashier,model_tresorerie_payment,group_treasury_cashier,1,1,1,0
access_payment_chief,tresorerie.payment.chief,model_tresorerie_payment,group_treasury_chief,1,1,1,1
access_payment_controller,tresorerie.payment.controller,model_tresorerie_payment,group_treasury_controller,1,0,0,0

# Treasury Receipt Access
access_receipt_cashier,tresorerie.receipt.cashier,model_tresorerie_receipt,group_treasury_cashier,1,1,1,0
access_receipt_chief,tresorerie.receipt.chief,model_tresorerie_receipt,group_treasury_chief,1,1,1,1
access_receipt_controller,tresorerie.receipt.controller,model_tresorerie_receipt,group_treasury_controller,1,0,0,0

# Treasury Lettrage Access
access_lettrage_cashier,tresorerie.lettrage.cashier,model_tresorerie_lettrage,group_treasury_cashier,1,1,1,0
access_lettrage_chief,tresorerie.lettrage.chief,model_tresorerie_lettrage,group_treasury_chief,1,1,1,1
access_lettrage_controller,tresorerie.lettrage.controller,model_tresorerie_lettrage,group_treasury_controller,1,0,0,0

# Treasury Refund Access
access_refund_cashier,tresorerie.refund.cashier,model_tresorerie_refund,group_treasury_cashier,1,1,1,0
access_refund_chief,tresorerie.refund.chief,model_tresorerie_refund,group_treasury_chief,1,1,1,1
access_refund_controller,tresorerie.refund.controller,model_tresorerie_refund,group_treasury_controller,1,0,0,0
