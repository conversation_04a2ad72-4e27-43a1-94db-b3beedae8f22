# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class PartialPaymentWizard(models.TransientModel):
    _name = 'tresorerie.partial.payment.wizard'
    _description = 'Assistant de Paiement Partiel'

    lettrage_id = fields.Many2one('tresorerie.lettrage', string='Lettrage', required=True)
    remaining_amount = fields.Monetary(string='Montant Restant', readonly=True)
    partial_amount = fields.Monetary(string='Montant du Paiement Partiel', required=True)
    currency_id = fields.Many2one('res.currency', related='lettrage_id.currency_id')
    payment_method = fields.Selection([
        ('cash', 'Espèces'),
        ('check', 'Chèque'),
        ('transfer', 'Virement'),
        ('card', 'Carte Bancaire')
    ], string='Mode de Paiement', required=True, default='cash')
    
    # Check details
    check_number = fields.Char(string='Numéro de Chèque')
    check_date = fields.Date(string='Date du Chèque')
    bank_id = fields.Many2one('res.bank', string='Banque')
    
    # Transfer details
    transfer_reference = fields.Char(string='Référence Virement')
    
    @api.model
    def default_get(self, fields_list):
        """Set default lettrage from context"""
        res = super().default_get(fields_list)
        if self.env.context.get('active_model') == 'tresorerie.lettrage' and self.env.context.get('active_id'):
            lettrage = self.env['tresorerie.lettrage'].browse(self.env.context['active_id'])
            res.update({
                'lettrage_id': lettrage.id,
                'remaining_amount': lettrage.remaining_amount,
            })
        return res
    
    @api.onchange('partial_amount')
    def _onchange_partial_amount(self):
        """Validate partial amount"""
        if self.partial_amount and self.remaining_amount:
            if self.partial_amount > self.remaining_amount:
                return {
                    'warning': {
                        'title': _('Montant Invalide'),
                        'message': _('Le montant partiel ne peut pas dépasser le montant restant.')
                    }
                }
    
    def action_create_partial_payment(self):
        """Create a partial payment"""
        self.ensure_one()
        
        if not self.lettrage_id:
            raise UserError(_("Aucun lettrage sélectionné."))
            
        if self.lettrage_id.state != 'active':
            raise UserError(_("Le lettrage doit être actif pour créer un paiement partiel."))
        
        if self.partial_amount <= 0:
            raise UserError(_("Le montant du paiement partiel doit être positif."))
            
        if self.partial_amount > self.remaining_amount:
            raise UserError(_("Le montant partiel ne peut pas dépasser le montant restant."))
        
        # Validate check details
        if self.payment_method == 'check':
            if not self.check_number or not self.check_date or not self.bank_id:
                raise UserError(_("Les détails du chèque sont obligatoires."))
        
        # Validate transfer details
        if self.payment_method == 'transfer':
            if not self.transfer_reference:
                raise UserError(_("La référence du virement est obligatoire."))
        
        # Create new payment
        payment_vals = {
            'bordereau_id': self.lettrage_id.bordereau_id.id,
            'invoice_id': self.lettrage_id.invoice_id.id,
            'partner_id': self.lettrage_id.partner_id.id,
            'amount_paid': self.partial_amount,
            'payment_method': self.payment_method,
            'caisse_id': self.env.context.get('default_caisse_id'),
            'state': 'draft',
        }
        
        # Add payment method specific details
        if self.payment_method == 'check':
            payment_vals.update({
                'check_number': self.check_number,
                'check_date': self.check_date,
                'bank_id': self.bank_id.id,
            })
        elif self.payment_method == 'transfer':
            payment_vals.update({
                'transfer_reference': self.transfer_reference,
            })
        
        new_payment = self.env['tresorerie.payment'].create(payment_vals)
        
        # Log message on original lettrage
        self.lettrage_id.message_post(
            body=_("Paiement partiel créé: %s pour %s") % (
                new_payment.name, 
                self.partial_amount
            ),
            message_type='notification'
        )
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Paiement Partiel'),
            'res_model': 'tresorerie.payment',
            'res_id': new_payment.id,
            'view_mode': 'form',
            'target': 'current',
        }
