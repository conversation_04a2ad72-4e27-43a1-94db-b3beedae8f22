from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

class PortFee(models.Model):
    _name = 'caisse.port.fee'
    _description = 'Redevance Portuaire'
    _order = 'date desc'

    name = fields.Char('Référence', required=True, default='Nouveau')
    vessel_name = fields.Char('Nom du navire', required=True)
    vessel_type = fields.Selection([
        ('cargo', 'Cargo'),
        ('passenger', 'Passager'),
        ('fishing', 'Pêche'),
        ('other', 'Autre')
    ], 'Type de navire', required=True)
    
    tonnage = fields.Float('Tonnage', required=True)
    fee_rate = fields.Monetary('Taux de redevance', required=True, currency_field='currency_id')
    
    company_id = fields.Many2one('res.company', 'Société', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', 'Devise', related='company_id.currency_id')
    
    amount = fields.Monetary('Montant', required=True, currency_field='currency_id')
    total_amount = fields.Monetary('Montant total', compute='_compute_total_amount', store=True, currency_field='currency_id')
    date = fields.Date('Date', required=True, default=fields.Date.today)
    description = fields.Text('Description')
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('confirmed', 'Confirmé'),
        ('paid', 'Payé')
    ], default='draft')
    
    caisse_id = fields.Many2one('caisse.caisse', 'Caisse')
    movement_id = fields.Many2one('caisse.movement', 'Mouvement associé')
    payment_date = fields.Datetime('Date de paiement', readonly=True)

    @api.depends('fee_rate', 'tonnage')
    def _compute_total_amount(self):
        for fee in self:
            fee.total_amount = fee.fee_rate * fee.tonnage if fee.tonnage and fee.fee_rate else 0

    @api.model
    def create(self, vals):
        if vals.get('name', 'Nouveau') == 'Nouveau':
            sequence = self.env['ir.sequence'].next_by_code('caisse.port.fee')
            vals['name'] = sequence or 'REP'
        return super().create(vals)

    @api.constrains('tonnage')
    def _check_tonnage(self):
        for fee in self:
            if fee.tonnage <= 0:
                raise ValidationError(_('Le tonnage doit être positif.'))

    def confirm_fee(self):
        """Confirmer la redevance"""
        self.ensure_one()
        if self.state != 'draft':
            raise UserError(_('Seules les redevances en brouillon peuvent être confirmées.'))
        self.state = 'confirmed'

    def register_payment(self):
        """Enregistrer le paiement de la redevance"""
        self.ensure_one()
        if self.state != 'confirmed':
            raise UserError(_('Seules les redevances confirmées peuvent être payées.'))
        
        # Create movement in caisse
        if self.caisse_id:
            movement = self.env['caisse.movement'].create({
                'name': f'Redevance portuaire - {self.vessel_name}',
                'caisse_id': self.caisse_id.id,
                'type': 'in',
                'category': 'port_fee',
                'amount': self.total_amount,
                'description': f'Redevance portuaire pour {self.vessel_name} ({self.tonnage} tonnes)',
                'date': fields.Datetime.now(),
            })
            self.movement_id = movement.id
            movement.validate_movement()
        
        self.payment_date = fields.Datetime.now()
        self.state = 'paid'

    def cancel_fee(self):
        """Annuler la redevance"""
        self.ensure_one()
        if self.state == 'paid' and self.movement_id:
            raise UserError(_('Impossible d\'annuler une redevance payée avec mouvement associé.'))
        self.state = 'draft'








