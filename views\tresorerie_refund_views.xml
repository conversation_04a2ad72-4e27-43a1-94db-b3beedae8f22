<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Refund Tree View -->
    <record id="view_tresorerie_refund_tree" model="ir.ui.view">
        <field name="name">tresorerie.refund.tree</field>
        <field name="model">tresorerie.refund</field>
        <field name="arch" type="xml">
            <tree string="Remboursements" decoration-info="state=='draft'" decoration-warning="state in ['submitted','approved']" decoration-success="state=='processed'" decoration-danger="state in ['rejected','cancelled']">
                <field name="name"/>
                <field name="date"/>
                <field name="partner_name"/>
                <field name="refund_amount" widget="monetary"/>
                <field name="refund_type"/>
                <field name="reason"/>
                <field name="refund_method"/>
                <field name="state" widget="badge"/>
                <field name="requested_by"/>
                <field name="approved_by"/>
            </tree>
        </field>
    </record>

    <!-- Refund Form View -->
    <record id="view_tresorerie_refund_form" model="ir.ui.view">
        <field name="name">tresorerie.refund.form</field>
        <field name="model">tresorerie.refund</field>
        <field name="arch" type="xml">
            <form string="Remboursement">
                <header>
                    <button name="action_submit" type="object" string="Soumettre" class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="action_approve" type="object" string="Approuver" class="btn-success" attrs="{'invisible': [('state', '!=', 'submitted')]}" groups="tresorie.group_treasury_chief"/>
                    <button name="action_process" type="object" string="Traiter" class="btn-info" attrs="{'invisible': [('state', '!=', 'approved')]}" groups="tresorie.group_treasury_chief"/>
                    <button name="action_reject" type="object" string="Rejeter" class="btn-danger" attrs="{'invisible': [('state', 'not in', ['submitted', 'approved'])]}" groups="tresorie.group_treasury_chief"/>
                    <button name="action_cancel" type="object" string="Annuler" attrs="{'invisible': [('state', 'in', ['processed', 'cancelled'])]}"/>
                    <button name="action_reset_to_draft" type="object" string="Remettre en brouillon" attrs="{'invisible': [('state', 'in', ['draft', 'processed'])]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,submitted,approved,processed"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_payment" type="object" class="oe_stat_button" icon="fa-money">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Paiement Original</span>
                            </div>
                        </button>
                        <button name="action_view_movement" type="object" class="oe_stat_button" icon="fa-exchange" attrs="{'invisible': [('movement_id', '=', False)]}">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Mouvement</span>
                            </div>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="date"/>
                            <field name="payment_id" options="{'no_create': True}" domain="[('state', '=', 'validated')]"/>
                            <field name="partner_id" readonly="1"/>
                            <field name="partner_name" readonly="1"/>
                        </group>
                        <group>
                            <field name="caisse_id" options="{'no_create': True}"/>
                            <field name="requested_by" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Montants">
                            <group>
                                <group>
                                    <field name="original_amount" widget="monetary" readonly="1"/>
                                    <field name="refund_amount" widget="monetary"/>
                                    <field name="remaining_amount" widget="monetary" readonly="1"/>
                                    <field name="currency_id" invisible="1"/>
                                    <field name="company_id" invisible="1"/>
                                </group>
                                <group>
                                    <field name="refund_type" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Motif">
                            <group>
                                <group>
                                    <field name="reason" widget="radio"/>
                                </group>
                            </group>
                            <group>
                                <field name="reason_description" placeholder="Décrivez en détail le motif du remboursement..."/>
                            </group>
                        </page>
                        
                        <page string="Mode de Remboursement">
                            <group>
                                <group>
                                    <field name="refund_method" widget="radio"/>
                                </group>
                            </group>
                            
                            <group string="Détails Chèque" attrs="{'invisible': [('refund_method', '!=', 'check')]}">
                                <group>
                                    <field name="check_number"/>
                                    <field name="check_date"/>
                                </group>
                            </group>
                            
                            <group string="Détails Virement" attrs="{'invisible': [('refund_method', '!=', 'transfer')]}">
                                <group>
                                    <field name="bank_account"/>
                                    <field name="transfer_reference"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Validation">
                            <group>
                                <group>
                                    <field name="approved_by" readonly="1"/>
                                    <field name="approval_date" readonly="1"/>
                                    <field name="processed_by" readonly="1"/>
                                    <field name="processing_date" readonly="1"/>
                                </group>
                                <group>
                                    <field name="rejected_by" readonly="1" attrs="{'invisible': [('state', '!=', 'rejected')]}"/>
                                    <field name="rejection_date" readonly="1" attrs="{'invisible': [('state', '!=', 'rejected')]}"/>
                                </group>
                            </group>
                            
                            <group string="Motif de Rejet" attrs="{'invisible': [('state', '!=', 'rejected')]}">
                                <field name="rejection_reason" readonly="1"/>
                            </group>
                        </page>
                        
                        <page string="Références">
                            <group>
                                <group>
                                    <field name="bordereau_id" readonly="1"/>
                                    <field name="invoice_id" readonly="1"/>
                                    <field name="receipt_id" readonly="1"/>
                                </group>
                                <group>
                                    <field name="movement_id" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Pièces Justificatives">
                            <field name="attachment_ids" widget="many2many_binary"/>
                        </page>
                        
                        <page string="Audit">
                            <group>
                                <group>
                                    <field name="created_date" readonly="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Refund Search View -->
    <record id="view_tresorerie_refund_search" model="ir.ui.view">
        <field name="name">tresorerie.refund.search</field>
        <field name="model">tresorerie.refund</field>
        <field name="arch" type="xml">
            <search string="Rechercher Remboursements">
                <field name="name" string="Numéro"/>
                <field name="partner_name" string="Redevable"/>
                <field name="payment_id" string="Paiement"/>
                <field name="requested_by" string="Demandé par"/>
                <field name="approved_by" string="Approuvé par"/>
                
                <filter name="draft" string="Brouillon" domain="[('state', '=', 'draft')]"/>
                <filter name="submitted" string="Soumis" domain="[('state', '=', 'submitted')]"/>
                <filter name="approved" string="Approuvé" domain="[('state', '=', 'approved')]"/>
                <filter name="processed" string="Traité" domain="[('state', '=', 'processed')]"/>
                <filter name="rejected" string="Rejeté" domain="[('state', '=', 'rejected')]"/>
                <filter name="cancelled" string="Annulé" domain="[('state', '=', 'cancelled')]"/>
                
                <separator/>
                <filter name="partial" string="Remboursement Partiel" domain="[('refund_type', '=', 'partial')]"/>
                <filter name="total" string="Remboursement Total" domain="[('refund_type', '=', 'total')]"/>
                
                <separator/>
                <filter name="my_requests" string="Mes Demandes" domain="[('requested_by', '=', uid)]"/>
                <filter name="pending_approval" string="En Attente d'Approbation" domain="[('state', 'in', ['submitted', 'approved'])]"/>
                
                <separator/>
                <filter name="today" string="Aujourd'hui" domain="[('date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('date', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <filter name="this_week" string="Cette semaine" domain="[('date', '&gt;=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                <filter name="this_month" string="Ce mois" domain="[('date', '&gt;=', context_today().strftime('%Y-%m-01'))]"/>
                
                <group expand="0" string="Grouper par">
                    <filter name="group_state" string="Statut" context="{'group_by': 'state'}"/>
                    <filter name="group_refund_type" string="Type" context="{'group_by': 'refund_type'}"/>
                    <filter name="group_reason" string="Motif" context="{'group_by': 'reason'}"/>
                    <filter name="group_refund_method" string="Mode de remboursement" context="{'group_by': 'refund_method'}"/>
                    <filter name="group_requested_by" string="Demandé par" context="{'group_by': 'requested_by'}"/>
                    <filter name="group_date" string="Date" context="{'group_by': 'date'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_tresorerie_refund" model="ir.actions.act_window">
        <field name="name">Remboursements</field>
        <field name="res_model">tresorerie.refund</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_this_month': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Créer votre première demande de remboursement
            </p>
            <p>
                Les remboursements permettent de restituer tout ou partie d'un paiement
                après validation par le chef de trésorerie.
            </p>
        </field>
    </record>

    <!-- Action for pending approvals -->
    <record id="action_refund_pending_approval" model="ir.actions.act_window">
        <field name="name">Remboursements à Approuver</field>
        <field name="res_model">tresorerie.refund</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', 'in', ['submitted', 'approved'])]</field>
    </record>

    <!-- Action for my refund requests -->
    <record id="action_my_refund_requests" model="ir.actions.act_window">
        <field name="name">Mes Demandes de Remboursement</field>
        <field name="res_model">tresorerie.refund</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_my_requests': 1, 'search_default_this_month': 1}</field>
    </record>
</odoo>
