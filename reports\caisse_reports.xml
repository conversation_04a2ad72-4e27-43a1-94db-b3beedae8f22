<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Balance Report Tree View -->
    <record id="view_caisse_balance_report" model="ir.ui.view">
        <field name="name">caisse.balance.report.tree</field>
        <field name="model">caisse.caisse</field>
        <field name="arch" type="xml">
            <tree string="Rapport des Soldes" create="false" edit="false">
                <field name="name"/>
                <field name="code"/>
                <field name="responsible_id"/>
                <field name="balance" sum="Total"/>
                <field name="theoretical_balance" sum="Total Théorique"/>
                <field name="current_session_id"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Cash Balance Report -->
    <record id="action_caisse_balance_report" model="ir.actions.act_window">
        <field name="name">Rapport des Soldes</field>
        <field name="res_model">caisse.caisse</field>
        <field name="view_mode">tree</field>
    </record>

    <!-- Movement Pivot View -->
    <record id="view_caisse_movement_pivot" model="ir.ui.view">
        <field name="name">caisse.movement.pivot</field>
        <field name="model">caisse.movement</field>
        <field name="arch" type="xml">
            <pivot string="Analyse des Mouvements">
                <field name="caisse_id" type="row"/>
                <field name="category" type="row"/>
                <field name="type" type="col"/>
                <field name="signed_amount" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- Cash Flow Report Action -->
    <record id="action_caisse_cash_flow_report" model="ir.actions.act_window">
        <field name="name">Rapport de Flux de Trésorerie</field>
        <field name="res_model">caisse.movement</field>
        <field name="view_mode">pivot,graph</field>
        <field name="domain">[]</field>
        <field name="context">{
            'search_default_group_caisse': 1,
            'search_default_group_date': 1
        }</field>
    </record>
</odoo>

